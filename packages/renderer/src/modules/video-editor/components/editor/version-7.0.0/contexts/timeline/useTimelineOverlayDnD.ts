import React, { RefObject, useCallback, useRef, useState } from 'react'
import {
  IndexableOverlay,
  IndexableTrack,
  Overlay,
  OverlayType,
  StoryboardOverlay,
  Track,
  TrackType
} from '@app/rve-shared/types'
import {
  byStartFrame,
  byStoryboard, byTypeIfInNarrationTrack,
  calculateLeftSpaceOfStoryboard,
  findOverlay,
  findOverlaysAboveStorybook,
  findOverlaysBetweenFrames,
  findOverlayStoryboard,
  findTrackByOverlay,
  getOverlayTrackIndex,
} from '@rve/editor/utils/overlay-helper.ts'
import { getOverlayTimeRange } from '@app/rve-shared/utils'
import { useEditorContext } from '../editor.context'
import { OVERLAY_THRESHOLD_WHEN_DRAG_TO_SWAP, PIXELS_PER_FRAME } from '@rve/editor/constants'
import { findStoryboardByFromFrame, isOverlayAcceptableByTrack } from '@rve/editor/utils/track-helper.ts'
import { SingleOverlayUpdatePayload } from '@rve/editor/contexts/editor/useOverlays.tsx'
import { GhostElement } from '../../types'
import { clamp } from 'lodash'

/**
 * Key: Overlay ID
 */
class OverlaysAdjustment extends Map<number, {
  fromFrameShift?: number
  durationShift?: number
  targetStoryboardIndex?: number
}> {

  public apply(another: OverlaysAdjustment) {
    another.forEach((value, key) => {
      this.set(key, value)
    })
  }
}

interface DraggableState {
  /**
   * 是否允许拖拽 (无论为何种 `DragAction`)
   */
  draggable: boolean

  /**
   * 拖拽导致的其他 Overlay 的变动
   */
  overlaysAdjust?: OverlaysAdjustment

  /**
   * 拖拽过后, 目标 Overlay 的起始帧
   */
  adjustedStartFrame?: number

  /**
   * 拖拽过后, 目标 Overlay 的总时长
   */
  adjustedDuration?: number

  /**
   * 拖拽过后, 目标 Overlay 所处的轨道序号
   */
  adjustedRow?: number

  /**
   * 拖拽过后, 目标 Overlay 所处的分镜序号
   */
  targetStoryboardIndex?: number
}

type DragAction = 'move' | 'resize-end'

// Type for the drag information stored in the ref
export interface DragInfoState {
  id: number
  overlay: Overlay
  action: DragAction

  initialFrom: number
  initialDurationInFrames: number
  initialRow: number

  landingPoint?: GhostElement
  draggableState?: DraggableState

  currentRow?: number
}

export type TimelineOverlayDnDHook = {
  /**
   * 指示当前是否在进行 Overlay 拖拽
   */
  isDragging: boolean

  dragInfo: RefObject<DragInfoState| null>

  /**
   * 当前拖拽中的 Overlay
   */
  draggingOverlay: Overlay | null

  /**
   * 用于展示鼠标当前拖动位置
   */
  mousePosition: GhostElement | null

  /**
   * 用于指示当前拖拽的 Overlay 的最终落点
   */
  landingPoint: GhostElement | null

  /**
   * 用于预览即将会发生的 Overlay 飘移
   */
  previewOverlaysAdjust: OverlaysAdjustment

  mouseOnCurrentFrame: number | null

  handleOverlayDragStart(overlay: Overlay, action: DragAction): void
  handleOverlayDragMove(deltaX: number, targetTrackIndex?: number): void
  handleOverlayDragEnd(): void

  handleMouseMove(e: React.MouseEvent<HTMLDivElement>): void
  handleOverlayDragOverBound(): void
}

function snapToGrid(value: number) {
  const GRID_SIZE = 1 // Assuming frame-level snapping
  return Math.round(value / GRID_SIZE) * GRID_SIZE
}

class AdjustCalculator {

  constructor(private readonly tracks: Track[]) {
  }

  /**
   * 计算调整大小情况下的 OverlaysAdjust
   * @param currentOverlay 当前正在调整的 Overlay
   * @param restOverlays 当前轨道中, 除当前 Overlay 以外的其他 Overlay
   * @param intendedNewDuration 目标时长
   * @param targetStoryboard 当前 Overlay 所处的分镜
   * @param shouldFillGap 当时间缩短时, 是否需要将后方的 Overlay 前移, 以填补空隙
   */
  public calcAdjustForResize(
    currentOverlay: Overlay,
    restOverlays: Overlay[],
    intendedNewDuration: number,
    targetStoryboard: Overlay | null,
    shouldFillGap?: boolean
  ): {
    overlaysAdjust: OverlaysAdjustment,
    adjustedDuration: number
  } {
    let adjustedDuration = intendedNewDuration
    // 当在分镜中调整时长时, 需要考虑分镜内的剩余空间, 并限制时长
    if (targetStoryboard && currentOverlay.type !== OverlayType.STORYBOARD) {
      // 计算剩余空间时，需要把当前 Overlay 也包含在内
      const leftSpace = calculateLeftSpaceOfStoryboard(
        targetStoryboard,
        [currentOverlay, ...restOverlays]
      )

      adjustedDuration = Math.min(
        currentOverlay.durationInFrames + leftSpace,
        intendedNewDuration
      )
    }

    const overlaysAdjust: OverlaysAdjustment = new OverlaysAdjustment()

    const [, currentOverlayEndFrame] = getOverlayTimeRange(currentOverlay)

    const intendedNewEnd = adjustedDuration + currentOverlay.from

    const affectedOverlays = findOverlaysBetweenFrames(
      restOverlays,
      currentOverlayEndFrame - 1,
      Infinity,
      'start'
    )

    if (!affectedOverlays.length) {
      return { overlaysAdjust, adjustedDuration }
    }

    // 移动当前 Overlay 后方的所有 Overlay, 以补齐空位或避免重叠
    // 如果当前 Overlay 是分镜 Overlay, 则该分镜下的所有 Overlay 也需要同步移动
    const allOverlays = currentOverlay.type === OverlayType.STORYBOARD
      ? affectedOverlays
        .filter((o): o is StoryboardOverlay & { index: number } => o.type === OverlayType.STORYBOARD)
        .map(storyboard => {
          return [storyboard, ...findOverlaysAboveStorybook(this.tracks, storyboard)]
        })
        .flat()
      : affectedOverlays

    const durationShift = intendedNewEnd - affectedOverlays[0].from

    if (durationShift > 0 || shouldFillGap) {
      allOverlays.forEach(storyboard => {
        overlaysAdjust.set(storyboard.id, { fromFrameShift: durationShift })
      })
    }

    return { overlaysAdjust, adjustedDuration }
  }

  /**
   * 计算移动情况下的 OverlaysAdjust
   * @param currentOverlay 当前正在调整的 Overlay
   * @param originalStoryboard 当前 Overlay 所处的原始分镜
   * @param originalTrack 当前 Overlay 所处的原始轨道
   * @param targetStoryboard 拖拽目标分镜
   * @param targetTrack 拖拽目标轨道
   * @param intendedStartFrame 目标起始帧
   */
  public calcAdjustForMoving(
    currentOverlay: Overlay,
    originalStoryboard: IndexableOverlay | null,
    originalTrack: IndexableTrack,
    targetStoryboard: IndexableOverlay | null,
    targetTrack: IndexableTrack,
    intendedStartFrame: number,
  ): DraggableState {
    if (
      // 处理分镜的拖动
      targetTrack.type === TrackType.STORYBOARD && currentOverlay.type === OverlayType.STORYBOARD
      // 组内移动
      || (
        originalTrack.index === targetTrack.index
        && originalStoryboard
        && targetStoryboard
        && originalStoryboard.index === targetStoryboard.index
        && targetTrack.type !== TrackType.NARRATION
      )
    ) {
      const { overlaysAdjust, fromFrameShift } = this.calcAdjustForInsideMoving(
        currentOverlay,
        targetTrack.overlays.filter(byStoryboard(originalStoryboard)),
        intendedStartFrame,
      )

      return {
        draggable: true,
        overlaysAdjust,
        adjustedStartFrame: currentOverlay.from + fromFrameShift,
        adjustedRow: originalTrack.index,
      }
    }

    let adjustedStartFrame = intendedStartFrame
    const overlaysAdjust = new OverlaysAdjustment()

    const handlers = {
      stickToLeft: !targetTrack.isGlobalTrack && targetTrack.type !== TrackType.NARRATION && !!targetStoryboard,
      removeGap: !originalTrack.isGlobalTrack && originalTrack.type !== TrackType.NARRATION && !!originalStoryboard
    }

    if (handlers.removeGap) {
      overlaysAdjust.apply(
        this.#fillGapOfTracks(
          currentOverlay,
          originalTrack.overlays.filter(byStoryboard(originalStoryboard))
        )
      )
    }

    if (handlers.stickToLeft) {
      const positionToStick = this.#findPositionToStick(
        targetTrack.overlays.filter(byStoryboard(targetStoryboard)),
        intendedStartFrame
      )

      adjustedStartFrame = positionToStick ?? targetStoryboard!.from
    }

    if (!targetTrack.isGlobalTrack && targetTrack.type === TrackType.VIDEO) {
      overlaysAdjust.apply(
        this.#pushOverlappedOverlays(
          targetTrack.overlays.filter(byStoryboard(targetStoryboard)),
          adjustedStartFrame,
          currentOverlay.durationInFrames
        )
      )
    } else {
      const [from, adjust] = this.calcMovingToGlobalTrack(
        currentOverlay,
        targetTrack.overlays
          .filter(o => o.id !== currentOverlay.id)
          .filter(byTypeIfInNarrationTrack(currentOverlay, targetTrack)),
        adjustedStartFrame
      )
      adjustedStartFrame = from
      overlaysAdjust.apply(adjust)
    }

    return {
      draggable: true,
      overlaysAdjust,
      adjustedStartFrame,
      targetStoryboardIndex: targetStoryboard?.index
    }
  }

  #pushOverlappedOverlays(targetOverlays: Overlay[], startFrame: number, distanceToPush: number): OverlaysAdjustment {
    return new OverlaysAdjustment(
      targetOverlays
        .filter(o => {
          return o.from >= startFrame
        })
        .map(o => [o.id, { fromFrameShift: distanceToPush }])
    )
  }

  #findPositionToStick(
    targetOverlays: Overlay[],
    intendedNewFrom: number,
  ): number | null {
    const target = targetOverlays
      .sort(byStartFrame())
      .find(o => {
        return (o.from + o.durationInFrames / 2)  > intendedNewFrom
      })

    if (target) return target.from
    if (targetOverlays.at(-1)) {
      const [, lastEnd] = getOverlayTimeRange(targetOverlays.at(-1))
      return lastEnd
    }
    return null
  }

  /**
   * 用于从 `sourceOverlays` 中移走 `currentOverlay` 时, 将其后方的 Overlay 前移, 以填补空隙
   * @param removedOverlay
   * @param sourceOverlays
   * @private
   */
  #fillGapOfTracks(
    removedOverlay: Overlay,
    sourceOverlays: Overlay[],
  ) {
    const overlaysAdjust = new OverlaysAdjustment()

    const affectedOverlays = findOverlaysBetweenFrames(
      sourceOverlays,
      removedOverlay.from + removedOverlay.durationInFrames - 1,
      Infinity,
      'start'
    )
    affectedOverlays
      .filter(o => o.id !== removedOverlay.id)
      .forEach(o => {
        overlaysAdjust.set(o.id, { fromFrameShift: -removedOverlay.durationInFrames })
      })

    return overlaysAdjust
  }

  /**
   * 处理移动到全局轨道的情况
   */
  private calcMovingToGlobalTrack(
    currentOverlay: Overlay,
    targetOverlays: Overlay[],
    intendedNewFrom: number,
  ): [number, OverlaysAdjustment] {
    let adjustedFrom = intendedNewFrom
    const overlaysAdjust = new OverlaysAdjustment()

    const leftOverlapping = findOverlaysBetweenFrames(
      targetOverlays,
      -1,
      intendedNewFrom,
      'start',
    ).filter(o => o.from + o.durationInFrames > intendedNewFrom)
      .sort(byStartFrame('desc'))
      .at(0)

    // 左侧有重叠的情况下，则以重叠元素的结尾帧作为拖动的新起始帧
    if (leftOverlapping) {
      adjustedFrom = leftOverlapping.from + leftOverlapping.durationInFrames
    }

    const rightOverlapping = findOverlaysBetweenFrames(
      targetOverlays,
      // 若左侧有重叠时, 目标位置已向右偏移。所以后续应以 `adjustedFrom` 作为筛选的起始帧
      adjustedFrom - 1,
      Infinity,
      'start'
    ).filter(o => o.from < adjustedFrom + currentOverlay.durationInFrames)
      .sort(byStartFrame())
      .at(0)

    // 两侧都没有重叠的情况下, 则允许拖入并无需对目标轨道进行调整，
    if (!leftOverlapping && !rightOverlapping) {
      return [adjustedFrom, overlaysAdjust]
    }

    // 右侧有重叠的情况下, 则将重叠元素向后推移
    if (rightOverlapping) {
      const affectedOverlays = findOverlaysBetweenFrames(
        targetOverlays,
        rightOverlapping.from - 1,
        Infinity,
        'start'
      )

      const overlapSize = adjustedFrom + currentOverlay.durationInFrames - rightOverlapping.from

      affectedOverlays.forEach(o => {
        overlaysAdjust.set(o.id, { fromFrameShift: overlapSize })
      })
    }

    return [adjustedFrom, overlaysAdjust]
  }

  /**
   * 计算 "组内" 移动情况下的 OverlaysAdjust.
   * "组内"的定义: 在同一个轨道中, 且在同一个分镜中(如是分镜轨道的话)
   */
  private calcAdjustForInsideMoving(
    currentOverlay: Overlay,
    restOverlays: Overlay[],
    intendedNewFrom: number,
  ): {
    overlaysAdjust: OverlaysAdjustment
    fromFrameShift: number
  }  {
    const intendedNewDuration = currentOverlay.durationInFrames

    // 移动方向是否为时间轴的起始方向
    const isBackward = intendedNewFrom < currentOverlay.from
    const [currentFrom, currentEnd] = getOverlayTimeRange(currentOverlay)
    const intendedNewEnd = intendedNewFrom + intendedNewDuration

    // 根据移动方向和拖动位置, 筛选出受影响的分镜
    const affectedOverlays = findOverlaysBetweenFrames(
      restOverlays,
      isBackward ? intendedNewFrom : currentFrom,
      isBackward ? currentEnd : intendedNewEnd,
      isBackward ? 'end' : 'start',
      OVERLAY_THRESHOLD_WHEN_DRAG_TO_SWAP
    ).filter(o => o.id !== currentOverlay.id)

    if (!affectedOverlays.length) {
      return {
        overlaysAdjust: new OverlaysAdjustment(),
        fromFrameShift: 0
      }
    }

    const overlaysAdjust = new OverlaysAdjustment()

    const targetStoryboardIndex = affectedOverlays.at(isBackward ? 0 : -1)!.index

    // 计算所有受影响分镜的总时长; 同时处理受影响分镜(及其下 Overlay)的移动
    const totalDuration = affectedOverlays.reduce(
      (result, item) => {
        // 根据拖动方向, 移动所涉及的分镜
        const fromFrameShift = currentOverlay.durationInFrames * (isBackward ? 1 : -1)
        overlaysAdjust.set(item.id, {
          fromFrameShift
        })

        if (item.type === OverlayType.STORYBOARD) {
          // 分镜下的所有 Overlay 也要跟随移动, 并且需要更新其所在分镜序号
          findOverlaysAboveStorybook(this.tracks, item)
            .forEach(relatedOverlay => {
              overlaysAdjust.set(relatedOverlay.id, {
                fromFrameShift,
                ...(relatedOverlay.storyboardIndex !== undefined && {
                  targetStoryboardIndex: relatedOverlay.storyboardIndex + (isBackward ? 1 : -1)
                })
              })
            })
        }

        return result + item.durationInFrames
      },
      0
    )

    const shiftOfCurrentOverlay =  totalDuration * (isBackward ? -1 : 1)
    overlaysAdjust.set(currentOverlay.id, { fromFrameShift: shiftOfCurrentOverlay })

    if (currentOverlay.type === OverlayType.STORYBOARD) {
      findOverlaysAboveStorybook(this.tracks, currentOverlay)
        .forEach(relatedOverlay => {
          overlaysAdjust.set(relatedOverlay.id, {
            fromFrameShift: shiftOfCurrentOverlay,
            targetStoryboardIndex,
          })
        })
    }

    return {
      overlaysAdjust,
      fromFrameShift: shiftOfCurrentOverlay
    }
  }
}

const useCalculateDraggableState = () => {
  const { tracks } = useEditorContext()

  return useCallback((
    draggedItemId: number,
    intendedNewFrom: number,
    intendedNewDuration: number,
    targetTrack: IndexableTrack,
    action: DragAction
  ): DraggableState => {
    const currentOverlay = findOverlay(tracks, draggedItemId)
    const originalTrack = findTrackByOverlay(tracks, draggedItemId)

    // Fast failure return
    if (
      !currentOverlay
      || !originalTrack
      || !isOverlayAcceptableByTrack(currentOverlay, targetTrack)
    ) {
      return {
        draggable: false,
      }
    }

    const calculator = new AdjustCalculator(tracks)
    const targetStoryboard = targetTrack.isGlobalTrack
      ? null
      : findStoryboardByFromFrame(tracks, intendedNewFrom)
    if (targetStoryboard) {
      console.log(`target storyboard index: 分镜${targetStoryboard.index + 1}(id=${targetStoryboard?.id})`)
    }

    if (action === 'resize-end') {
      // 仅允许在同一个轨道内调整大小
      if (originalTrack.index !== targetTrack.index) {
        return {
          draggable: false,
          overlaysAdjust: new OverlaysAdjustment(),
          adjustedStartFrame: intendedNewFrom,
        }
      }

      const restOverlays = targetTrack
        .overlays
        .filter(o => (
          currentOverlay.storyboardIndex === undefined
          || o.storyboardIndex === currentOverlay.storyboardIndex
        ))
        .filter(o => o.id !== currentOverlay.id)
        .sort(byStartFrame())

      const { overlaysAdjust, adjustedDuration } = calculator.calcAdjustForResize(
        currentOverlay,
        restOverlays,
        intendedNewDuration,
        targetStoryboard,
        targetStoryboard !== null
      )

      return {
        draggable: true,
        overlaysAdjust,
        adjustedStartFrame: currentOverlay.from,
        adjustedDuration: adjustedDuration
      }
    }

    const originalStoryboard = findOverlayStoryboard(tracks, currentOverlay)

    return {
      ...calculator.calcAdjustForMoving(
        currentOverlay,
        originalStoryboard,
        originalTrack,
        targetStoryboard,
        targetTrack,
        intendedNewFrom,
      ),
    }
  }, [tracks])
}

const useCalculateDragPositionInfo = (
  zoomScale: number
) => {
  const { tracks, durationInFrames } = useEditorContext()

  return useCallback((
    dragInfo: DragInfoState,
    deltaX: number,
    targetTrackIndex?: number
  ) => {
    const deltaFrame = snapToGrid(deltaX / zoomScale / PIXELS_PER_FRAME)

    // 使用传入的 targetTrackIndex，如果没有则使用初始轨道
    const targetRow = dragInfo.action === 'move' && targetTrackIndex !== undefined
      ? clamp(targetTrackIndex, 0, tracks.length - 1)
      : dragInfo.initialRow

    let targetStartFrame: number = dragInfo.initialFrom
    let targetDuration: number = dragInfo.initialDurationInFrames
    switch (dragInfo.action) {
      case 'move':
        targetStartFrame = Math.max(0, targetStartFrame + deltaFrame)
        break
      case 'resize-end': {
        targetDuration = Math.max(1, targetDuration + deltaFrame)
        break
      }
    }

    return {
      targetRow,
      targetStartFrame,
      targetDuration,
    }
  }, [durationInFrames, zoomScale, tracks.length])
}

/**
 * Custom hook to manage the state related to timeline drag and drop operations.
 * Tracks the dragging state, dragged item, ghost element position, and ghost marker position.
 *
 * @returns An object containing state variables and functions to manage timeline drag state.
 */
export const useTimelineOverlayDnD = (
  timelineGridRef: RefObject<HTMLDivElement | null>,
  zoomScale: number,
  isContextMenuOpen: boolean
): TimelineOverlayDnDHook => {
  const { tracks, durationInFrames, bulkUpdateOverlays } = useEditorContext()

  const calculateDraggableState = useCalculateDraggableState()
  const calculatePositionInfo = useCalculateDragPositionInfo(zoomScale)

  const [isDragging, setIsDragging] = useState(false)
  const [mousePosition, setMousePosition] = useState<GhostElement | null>(null)
  const [landingPoint, setLandingPoint] = useState<GhostElement | null>(null)
  const [draggingOverlay, setDraggingOverlay] = useState<Overlay | null>(null)
  const [mouseOnCurrentFrame, setMouseOnCurrentFrame] = useState<number | null>(null)
  const [previewOverlaysAdjust, setPreviewOverlaysAdjust] = useState<OverlaysAdjustment>(new OverlaysAdjustment())

  const dragInfo = useRef<DragInfoState | null>(null)

  const resetDragState = () => {
    setIsDragging(false)
    setDraggingOverlay(null)
    setLandingPoint(null)
    setMousePosition(null)
    setPreviewOverlaysAdjust(new OverlaysAdjustment())
    setMouseOnCurrentFrame(null)
    dragInfo.current = null
  }

  const handleOverlayDragStart = useCallback<TimelineOverlayDnDHook['handleOverlayDragStart']>(
    (overlay, action) => {
      if (!timelineGridRef.current) {
        return
      }

      const row = getOverlayTrackIndex(tracks, overlay.id) || 0

      setIsDragging(true)
      setDraggingOverlay(overlay)

      dragInfo.current = {
        id: overlay.id,
        overlay,
        action,
        initialFrom: overlay.from,
        initialDurationInFrames: overlay.durationInFrames,
        initialRow: row,
      }

      setMousePosition({
        from: overlay.from,
        durationInFrames: overlay.durationInFrames,
        row,
        overlay,
      })
      setPreviewOverlaysAdjust(new OverlaysAdjustment())
    },
    [tracks]
  )

  const handleOverlayDragMove = useCallback<TimelineOverlayDnDHook['handleOverlayDragMove']>(
    (deltaX, targetTrackIndex) => {
      if (!dragInfo.current) return

      const { targetRow, targetStartFrame, targetDuration } = calculatePositionInfo(
        dragInfo.current,
        deltaX,
        targetTrackIndex
      )

      dragInfo.current.currentRow = targetRow
      const {
        draggable,
        overlaysAdjust,
        adjustedStartFrame,
        adjustedDuration = targetDuration,
        adjustedRow = targetRow
      } = dragInfo.current.draggableState
        = calculateDraggableState(
          dragInfo.current.id,
          targetStartFrame,
          targetDuration,
          { ...tracks[targetRow], index: targetRow },
          dragInfo.current.action
        )

      const newMousePosition = {
        overlay: dragInfo.current.overlay,
        from: targetStartFrame,
        durationInFrames: targetDuration,
        row: adjustedRow,
        invalid: !draggable
      }

      setMousePosition(newMousePosition)

      if (draggable) {
        const newLandingPoint: GhostElement = {
          ...newMousePosition,
          ...(adjustedStartFrame !== undefined && { from: adjustedStartFrame }),
          durationInFrames: adjustedDuration
        }
        setLandingPoint(newLandingPoint)
        dragInfo.current.landingPoint = newLandingPoint
      } else {
        setLandingPoint(null)
      }

      setPreviewOverlaysAdjust(overlaysAdjust || new OverlaysAdjustment())
    },
    [durationInFrames, calculateDraggableState, calculatePositionInfo],
  )

  const handleMouseMove = useCallback<TimelineOverlayDnDHook['handleMouseMove']>(
    event => {
      if (isContextMenuOpen) return

      const { clientX } =  event
      const rect = timelineGridRef.current?.getBoundingClientRect()
      if (rect) {
        setMouseOnCurrentFrame((clientX - rect.left) / PIXELS_PER_FRAME / zoomScale)
      }
      return
    },
    [zoomScale, isContextMenuOpen],
  )

  const handleOverlayDragEnd = useCallback(
    () => {
      const currentDragInfo = dragInfo.current

      if (
        !currentDragInfo
        || !currentDragInfo.landingPoint
        || !currentDragInfo.draggableState?.draggable
      ) {
        resetDragState()
        return
      }

      const { id } = currentDragInfo
      const originalTrack = findTrackByOverlay(tracks, id)
      if (!originalTrack) {
        return resetDragState()
      }

      const originalOverlay = findOverlay(originalTrack, currentDragInfo.id)

      if (!originalOverlay) {
        resetDragState()
        return
      }

      const predicatedRow = currentDragInfo.currentRow ?? currentDragInfo.initialRow

      const {
        overlaysAdjust, targetStoryboardIndex, draggable, adjustedStartFrame, adjustedDuration
      } = currentDragInfo.draggableState

      if (!draggable) {
        return resetDragState()
      }

      const itemsToUpdate: SingleOverlayUpdatePayload[] = []

      // Always add the dragged item update if the position is valid
      itemsToUpdate.push({
        ...originalOverlay,
        targetTrackIndex: predicatedRow,
        ...(adjustedStartFrame !== undefined && { from: adjustedStartFrame }),
        ...(adjustedDuration && { durationInFrames: adjustedDuration }),
        storyboardIndex: targetStoryboardIndex !== undefined
          ? targetStoryboardIndex
          : originalOverlay.storyboardIndex
      })

      if (overlaysAdjust) {
        overlaysAdjust.forEach(({
          fromFrameShift = 0,
          durationShift = 0,
          targetStoryboardIndex
        }, itemId) => {
          const overlayToAdjust = findOverlay(tracks, itemId)
          if (overlayToAdjust) {
            itemsToUpdate.push({
              ...overlayToAdjust,
              from: overlayToAdjust.from + fromFrameShift,
              durationInFrames: overlayToAdjust.durationInFrames + durationShift,
              ...(overlayToAdjust.type !== OverlayType.STORYBOARD && targetStoryboardIndex !== undefined && {
                storyboardIndex: targetStoryboardIndex
              })
            })
          }
        })
      }

      bulkUpdateOverlays(itemsToUpdate)
      resetDragState()
    },
    [tracks, dragInfo, durationInFrames, bulkUpdateOverlays]
  )

  // Handle when mouse leaves the timeline area
  const handleOverlayDragOverBound = useCallback(() => {
    // Remove ghost marker when mouse leaves
    setMouseOnCurrentFrame(null)
    // If it was dragging, end the drag operation
    if (isDragging) {
      resetDragState()
    }
  }, [isDragging])

  return {
    isDragging,
    draggingOverlay,
    dragInfo,

    mousePosition,
    landingPoint,
    previewOverlaysAdjust,
    mouseOnCurrentFrame,

    handleMouseMove,
    handleOverlayDragStart,
    handleOverlayDragMove,
    handleOverlayDragEnd,
    handleOverlayDragOverBound
  }
}
