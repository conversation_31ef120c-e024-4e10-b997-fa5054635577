import React, { memo, useCallback, useState, useMemo } from 'react'
import { useQuerySoundCategory, useInfiniteQuerySoundUnified } from '@/hooks/queries/useQuerySound.ts'
import { SoundResource } from '@/types/resources.ts'
import { ResourcePanelLayout } from '../overlays/common/resource-panel-layout.tsx'
import { Music } from 'lucide-react'
import { useResource } from '../../hooks/resource/useResource.tsx'
import { useResourceLoadingState } from '../../hooks/resource/useResourceLoadingState.tsx'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'
import { InfiniteResourceList } from '@/components/InfiniteResourceList.tsx'
import {  ResourceTabType, SoundResourceTabs } from '../../templates/sticker-templates/constants.ts'
import { AudioResourceItem } from '../overlays/common/audio-resource-item.tsx'
import { useOverlayHelper } from '../../hooks/helpers/useOverlayHelper.ts'
import { OverlayType, SoundOverlay } from '@app/rve-shared/types'

// 音效项组件
const SoundItem = memo(({
  item,
  isLoading,
  onItemClick,
  onItemAdd,
  onCollectionChange
}: {
  item: SoundResource.Sound
  isLoading: boolean
  onItemClick?: () => void
  onItemAdd: () => void
  onCollectionChange?: (collected: boolean) => void
}) => {
  const durationInSeconds = (item.content.durationMsec / 1000).toFixed(1) + 's'

  return (
    <div className="relative">
      <AudioResourceItem
        key={item.id}
        id={item.id}
        title={item.title}
        thumbnailUrl={item.cover?.url || ''}
        icon={<Music className="w-8 h-8" />}
        isLoading={isLoading}
        onClick={onItemClick}
        onAdd={onItemAdd}
        resourceType={ResourceType.SOUND}
        resourceUrl={item.content.itemUrl}
        description={durationInSeconds}
        audioUrl={item.content.itemUrl}
        interactInfo={item.interactInfo}
        onCollectionChange={onCollectionChange}
        showCollectionButton={true}
      />
    </div>
  )
})

SoundItem.displayName = 'SoundItem'

/**
 * 音效面板组件
 * 显示各种音效，并允许用户将音效添加到时间轴
 */
export function SoundsPanel() {
  const { downloadResourceToCache } = useResource()
  const { addOverlayToGlobalTrack } = useOverlayHelper()

  const { data: soundCategory } = useQuerySoundCategory()

  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [searchKey, setSearchKey] = useState<string>('')
  const infiniteQueryResult = useInfiniteQuerySoundUnified({
    pageSize: 12,
    selectedCategory: selectedCategory,
    search: searchKey
  })

  const handleCategoryChange = useCallback((value: string) => {
    setSelectedCategory(value)
  }, [])

  const handleSoundClick = useCallback(async (data: SoundResource.Sound) => {
    try {
      await downloadResourceToCache({
        url: data.content.itemUrl,
        resourceType: ResourceType.SOUND,
        id: data.id
      })

      const musicDurationInFrames = Math.round(data.content.durationMsec / 1000 * 30)

      const newOverlay: SoundOverlay = {
        id: Date.now(),
        type: OverlayType.SOUND,
        content: data.content.itemUrl,
        src: data.content.itemUrl,
        durationInFrames: musicDurationInFrames,
        from: 0,
        height: 100,
        width: 200,
        left: 0,
        top: 0,
        isDragging: false,
        rotation: 0,
        styles: {
          volume: 1,
        },
      }

      addOverlayToGlobalTrack(newOverlay)
    } catch (error) {
      console.error('下载音效失败:', error)
    }
  }, [downloadResourceToCache])

  const handleCollectionChange = useCallback((collected: boolean) => {
    console.log('音效收藏状态变更:', collected)
  }, [])

  // 声音项包装组件，处理异步加载状态
  const SoundItemWrapper = useCallback(({ item, index }: { item: SoundResource.Sound, index: number }) => {
    const isLoading = useResourceLoadingState(ResourceType.SOUND, item.content.itemUrl)

    return (
      <SoundItem
        key={`sound-${item.id}-${index}`}
        item={item}
        isLoading={isLoading}
        onItemAdd={() => handleSoundClick(item)}
        onCollectionChange={handleCollectionChange}
      />
    )
  }, [handleSoundClick, handleCollectionChange])

  const renderSoundItem = useCallback((item: SoundResource.Sound, index: number) => {
    return <SoundItemWrapper item={item} index={index} />
  }, [SoundItemWrapper])

  const renderSoundContent = useCallback(() => {
    return (
      <InfiniteResourceList
        queryResult={infiniteQueryResult}
        renderItem={renderSoundItem}
        emptyText="该分类暂无音效"
        loadingText="加载音效中..."
        itemsContainerClassName="grid grid-cols-4 gap-3 pt-3 pb-3"
      />
    )
  }, [infiniteQueryResult, renderSoundItem])

  const renderLocalStickerContent = useCallback(() => {
    // const exampleResources: LocalResourceItem[] = Array(8).fill(0).map((_, index) => ({
    //   id: `music-${index}`,
    //   type: 'music',
    //   name: `音乐 ${index + 1}`,
    //   path: ''
    // }))

    return (
      <div>
        1
      </div>
      // <LocalResourcePanel
      //   resources={exampleResources}
      //   emptyText="暂无本地音乐"
      //   renderResourceItem={(_resource, index) => (
      //     <div key={index} className="aspect-square bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
      //       <Music className="w-8 h-8 text-gray-400" />
      //     </div>
      //   )}
      // />
    )
  }, [])

  const hasSounds = useMemo(() => {
    const firstPage = infiniteQueryResult.data?.pages?.[0]
    return !!firstPage && firstPage.list.length > 0
  }, [infiniteQueryResult.data])

  // 配置标签页内容
  const tabsWithContent = useMemo(() => {
    return SoundResourceTabs.map(tab => ({
      ...tab,
      renderContent: () => {
        switch (tab.value) {
          case ResourceTabType.ONLINE:
            return renderSoundContent()
          case ResourceTabType.LOCAL:
            return renderLocalStickerContent()
          default:
            return null
        }
      },
      isEmpty: tab.value === ResourceTabType.ONLINE ? !hasSounds : false,
      emptyText: tab.value === ResourceTabType.ONLINE ? '该分类暂无音效' : '暂无本地音效',
    }))
  }, [renderSoundContent, renderLocalStickerContent, hasSounds])

  return (
    <ResourcePanelLayout
      tabs={tabsWithContent}
      defaultTab={ResourceTabType.ONLINE}
      categories={soundCategory}
      selectedCategory={selectedCategory}
      onCategoryChange={handleCategoryChange}
      searchKey={searchKey}
      onSearchChange={setSearchKey}
    />
  )
}

export default memo(SoundsPanel)
