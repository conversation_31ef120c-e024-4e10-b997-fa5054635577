import * as React from 'react'
import { useEffect, useMemo } from 'react'
import { SidebarMenuButton, } from '@/components/ui/sidebar.tsx'
import { useSidebar } from '@rve/editor/contexts'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger, } from '@/components/ui/tooltip.tsx'
import { clsx } from 'clsx'

import { getAllMaterialPlugins, getMaterialPlugin } from '@/modules/video-editor/plugins'
import { PageLoading } from '@/components/PageLoading'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'

function MaterialsSidebar() {
  const { activePanel, setActivePanel, setIsOpen } = useSidebar()

  const plugins = useMemo(() => getAllMaterialPlugins(), [])

  useEffect(() => {
    setActivePanel(plugins[0].id as any)
  }, [])

  return (
    <div className="bg-background border-r px-2 py-3">
      <TooltipProvider delayDuration={0}>
        {plugins.map(item => (
          <Tooltip key={item.id}>
            <TooltipTrigger asChild>
              <SidebarMenuButton
                onClick={() => {
                  // 将字符串ID转换为对应的OverlayType枚举值
                  setActivePanel(item.id as ResourceType)
                  setIsOpen(true)
                }}
                size="lg"
                className={clsx(
                  'flex flex-col items-center gap-2 px-1.5 py-2',
                  activePanel === item.id
                    ? 'bg-primary/10 text-primary hover:bg-primary/10'
                    : 'text-muted-foreground hover:bg-muted hover:text-foreground'
                )}
              >
                <item.icon
                  className="h-4 w-4 text-gray-700 dark:text-white font-light"
                  strokeWidth={1.25}
                />
                <span className="text-[8px] font-medium leading-none">
                  {item.title}
                </span>
              </SidebarMenuButton>
            </TooltipTrigger>
            <TooltipContent
              side="right"
              className="border bg-background text-foreground"
            >
              {item.title}
            </TooltipContent>
          </Tooltip>
        ))}
      </TooltipProvider>
    </div>
  )
}

function MaterialsContent() {
  const { activePanel } = useSidebar()

  const renderActivePanel = () => {
    const PluginComponent = getMaterialPlugin(activePanel)?.component
    if (!PluginComponent) return null

    return (
      <React.Suspense fallback={<PageLoading />}>
        <PluginComponent />
      </React.Suspense>
    )
  }

  return (
    <div className="h-full w-full items-center justify-center hidden flex-1 md:flex border-r overflow-y-auto">
      {renderActivePanel()}
    </div>
  )
}

export const Materials = {
  Sidebar: React.memo(MaterialsSidebar),
  Content: React.memo(MaterialsContent)
}
