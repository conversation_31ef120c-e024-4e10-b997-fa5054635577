import React, { useEffect } from 'react'
import type { VideoOverlay, StickerOverlay } from '@app/rve-shared/types'
import { FPS, ENABLE_VIDEO_KEYFRAME_EXTRACTING } from '../../constants'
import { useKeyframes } from '../../hooks/useKeyframes'
import { useAssetLoading } from '@rve/editor/contexts/asset-loading.context.tsx'

/**
 * Props for the TimelineKeyframes component
 */
interface TimelineKeyframesProps {
  /** The overlay object containing video/animation data */
  overlay: VideoOverlay | StickerOverlay
}

export const TimelineKeyframes: React.FC<TimelineKeyframesProps> = ({
  overlay,
}) => {
  const { handleAssetLoadingChange } = useAssetLoading()

  const { frames, previewFrames, isLoading } = useKeyframes({
    overlay,
  })

  useEffect(() => {
    if (isLoading) {
      handleAssetLoadingChange(overlay.id, isLoading)
    }
  }, [isLoading])

  // Only show loaded frames
  const loadedFrames = frames.filter(Boolean)
  const TOTAL_SLOTS = 5

  // Create an array of frames to display, filling remaining slots with the last loaded frame
  const displayFrames = Array(TOTAL_SLOTS)
    .fill(null)
    .map((_, index) => {
      if (index < loadedFrames.length) {
        return loadedFrames[index]
      }
      // Fill remaining slots with the last loaded frame
      return loadedFrames[loadedFrames.length - 1] || null
    })

  if (!ENABLE_VIDEO_KEYFRAME_EXTRACTING || loadedFrames.length <= 0) {
    return null
  }

  return (
    <div className="flex h-full overflow-hidden w-full bg-slate-100 dark:bg-gray-900">
      <div className="flex h-full w-full">
        {displayFrames.map((frame, index) => {
          const previewFrame = previewFrames[Math.min(index, previewFrames.length - 1)]
          const isLast = index === TOTAL_SLOTS - 1
          const timestamp = previewFrame ? Math.floor(previewFrame / FPS) : 0

          return (
            <div
              key={`${overlay.id}-${index}`}
              className={`relative ${!isLast ? 'border-r border-slate-300 dark:border-gray-700' : ''
              }`}
              style={{ width: `${100 / TOTAL_SLOTS}%` }}
            >
              {frame && (
                <div className="relative h-full w-full group">
                  <img
                    src={frame}
                    alt={`Frame at ${timestamp}s`}
                    className="object-cover transition-opacity group-hover:opacity-90"
                    style={{
                      imageRendering: 'crisp-edges',
                      objectFit: 'cover',
                    }}
                  />
                </div>
              )}
            </div>
          )
        })}
      </div>
    </div>
  )
}
