// Frames per second for video rendering
import { AspectRatio } from '@rve/editor/types'

export const FPS = 30

export const SHOW_LOADING_PROJECT_ALERT = false // Controls visibility of asset loading indicator

export const MINIMUM_FRAME_OF_OVERLAY = 1

/**
 * This constant disables video keyframe extraction in the browser. Enable this if you're working with
 * multiple videos or  large video files to improve performance. Keyframe extraction is CPU-intensive and can
 * cause browser lag. For production use, consider moving keyframe extraction to the server side.
 * Future versions of Remotion may provide more efficient keyframe handling.
 */
export const ENABLE_VIDEO_KEYFRAME_EXTRACTING = true

export const ENABLE_TRACK_DRAG = false

// Add new constant for push behavior
export const ENABLE_PUSH_ON_DRAG = false // Set to `false` to disable pushing items on drag

// NOTE: TO CHANGE RENDER TYPE, UPDATE THE RENDER_TYPE CONSTANT
export const RENDER_TYPE: 'ssr' | 'lambda' = 'ssr'

// Zoom control configuration
export const ZOOM_CONSTRAINTS = {
  min: 0.3, // Minimum zoom level
  max: 5, // Maximum zoom level
  step: 0.1, // Smallest increment for manual zoom controls
  default: 1, // Default zoom level
  zoomStep: 0.15, // Zoom increment for zoom in/out buttons
  wheelStep: 0.3, // Zoom increment for mouse wheel
  transitionDuration: 100, // Animation duration in milliseconds
  easing: 'cubic-bezier(0.4, 0.0, 0.2, 1)' // Smooth easing function for zoom transitions
}

// Timeline Snapping configuration
export const SNAPPING_CONFIG = {
  thresholdFrames: 1, // Default snapping sensitivity in frames
  enableVerticalSnapping: true // Enable snapping to items in adjacent rows
}

/**
 * 是否在轨道中间有间隙时显示 "移除间隙" 的按钮
 */
export const ENABLE_REMOVE_GAP_BUTTON = false

/**
 * 在时间轴上拖拽 Overlay 时, 两个重叠区域的重叠长度高于此值时才视为重叠
 * 单位: 帧
 */
export const OVERLAY_THRESHOLD_WHEN_DRAG_TO_SWAP = 15

// 自动保存的时间间隔
export const AUTO_SAVE_INTERVAL = 5000

export const SUPPORTED_ASPECT_RATIOS: AspectRatio[] = [
  '1:1',
  '3:4',
  '4:3',
  '4:5',
  '9:16',
  '16:9'
]
