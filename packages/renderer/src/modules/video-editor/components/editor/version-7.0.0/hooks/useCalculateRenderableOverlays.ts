import { useCallback } from 'react'
import { RenderableOverlay } from '@app/rve-shared/types'
import { useTimeline, useEditorContext } from '@rve/editor/contexts'
import { calculateRenderableOverlays } from '@rve/editor/utils/overlay-helper.ts'

/**
 * 根据 `videoActivation`, 从原始的轨道中筛选需要渲染的视频
 * TODO: 后续需要对 "口播" 轨道做同样的处理
 */
export const useCalculateRenderableOverlays = () => {
  const { tracks } = useEditorContext()
  const { videoActivation } = useTimeline()

  return useCallback(
    (): RenderableOverlay[] => calculateRenderableOverlays(tracks, videoActivation),
    [tracks, videoActivation]
  )
}
