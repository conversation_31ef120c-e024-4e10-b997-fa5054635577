import React, { ReactNode, useState, useEffect, useRef, useCallback } from 'react'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'
import { ResourceCacheIndicator } from '@/components/ResourceCacheIndicator'
import { useQueryResourceCacheStatus } from '@/hooks/queries/useQueryResourceCacheStatus'
import { InteractInfo } from '@/types/resources'
import { useResource } from '@rve/editor/hooks/resource/useResource.tsx'
import { cn } from '@/components/lib/utils'
import { ResourceCollectionIndicator } from '@/components/ResourceCollectionIndicator'
import { ImageOff } from 'lucide-react'

const AudioManager = {
  currentAudio: null as HTMLAudioElement | null,
  currentId: null as string | number | null,

  play(audio: HTMLAudioElement, id: string | number) {
    if (this.currentAudio && this.currentId !== id) {
      this.currentAudio.pause()
      const event = new CustomEvent('audiomanager:stopped', { detail: { id: this.currentId } })
      window.dispatchEvent(event)
    }

    this.currentAudio = audio
    this.currentId = id
    audio.play().catch(err => {
      console.error('播放音频失败:', err)
    })
  },

  stop(id: string | number) {
    if (this.currentId === id && this.currentAudio) {
      this.currentAudio.pause()
      this.currentAudio = null
      this.currentId = null
    }
  }
}

export interface AudioResourceItemProps {
  /**
   * 资源ID
   */
  id: string | number
  /**
   * 资源标题
   */
  title: string
  /**
   * 资源描述或副标题
   */
  description?: string | number
  /**
   * 资源缩略图URL
   */
  thumbnailUrl?: string
  /**
   * 默认图标 (Lucide图标)
   */
  icon?: ReactNode
  /**
   * 音频URL
   */
  audioUrl?: string
  /**
   * 是否正在加载
   */
  isLoading?: boolean
  /**
   * 点击添加按钮的回调
   */
  onAdd?: () => void
  /**
   * 点击整个资源项的回调
   */
  onClick?: () => void

  /**
   * 自定义内容
   */
  children?: ReactNode
  /**
   * 自定义类名
   */
  className?: string
  /**
   * 资源类型，用于检查本地缓存
   */
  resourceType?: ResourceType
  /**
   * 资源URL，用于检查本地缓存
   */
  resourceUrl?: string
  /**
   * 资源时长（毫秒）
   */
  durationMsec?: number
  /**
   * 自定义扩展名
   */
  customExt?: string,

  /**
   * 是否显示收藏按钮
   */
  showCollectionButton?: boolean
  /**
   * 交互信息，包含收藏状态
   */
  interactInfo?: InteractInfo
  /**
   * 收藏状态变更回调
   */
  onCollectionChange?: (collected: boolean) => void
}

/**
 * 音频资源项组件
 * 用于展示音频资源列表中的单个资源项，支持播放和显示进度
 */
export function AudioResourceItem({
  id,
  title,
  thumbnailUrl,
  icon,
  audioUrl,
  isLoading = false,
  onAdd,
  onClick,
  children,
  className = '',
  resourceType,
  resourceUrl,
  description,
  durationMsec,
  showCollectionButton = true,
  interactInfo,
  onCollectionChange,
  customExt,
}: AudioResourceItemProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [progress, setProgress] = useState(0) // 0-100
  const [audioInitialized, setAudioInitialized] = useState(false)
  const [imageLoadError, setImageLoadError] = useState(false)
  const audioRef = useRef<HTMLAudioElement | null>(null)

  const isCollected = interactInfo?.collected || false

  const { data: isCached = false } = useQueryResourceCacheStatus(resourceType, resourceUrl)

  const { getResourcePathSync } = useResource()

  // 当缩略图 URL 改变时重置图片加载错误状态
  useEffect(() => {
    setImageLoadError(false)
  }, [thumbnailUrl])

  const handleTimeUpdate = useCallback(() => {
    if (audioRef.current && audioRef.current.duration) {
      setProgress((audioRef.current.currentTime / audioRef.current.duration) * 100)
    }
  }, [])

  const handleAudioEnded = useCallback(() => {
    setIsPlaying(false)
    setProgress(0)
  }, [])

  const handleAudioError = useCallback(() => {
    setIsPlaying(false)
  }, [])

  const handleGlobalAudioStop = useCallback((event: Event) => {
    const customEvent = event as CustomEvent
    if (customEvent.detail && customEvent.detail.id !== id) {
      setIsPlaying(false)
    }
  }, [id])

  const collectionIndicator = resourceType && id && showCollectionButton && (
    <div className={cn(
      'absolute right-0 top-0 transition-opacity duration-200',
      isCollected ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
    )}
    >
      <ResourceCollectionIndicator
        resourceType={resourceType}
        resourceId={id}
        isCollected={isCollected}
        size={12}
        onCollectionChange={onCollectionChange}
      />
    </div>
  )

  const initializeAudio = useCallback(() => {
    if (audioInitialized || !audioUrl) return

    const audio = new Audio()
    audio.preload = 'metadata'
    audioRef.current = audio

    audio.addEventListener('timeupdate', handleTimeUpdate)
    audio.addEventListener('ended', handleAudioEnded)
    audio.addEventListener('error', handleAudioError)

    window.addEventListener('audiomanager:stopped', handleGlobalAudioStop)

    if (isCached && resourceType && resourceUrl) {
      const localPath = getResourcePathSync(resourceType, resourceUrl)
      if (localPath) {
        // 使用本地缓存路径
        audio.src = localPath
      } else {
        // 如果是音乐资源但没有扩展名，可能需要在前端处理
        audio.src = audioUrl
      }
    } else {
      audio.src = audioUrl
    }

    setAudioInitialized(true)

    return audio
  }, [audioUrl, handleTimeUpdate, handleAudioEnded, handleAudioError, handleGlobalAudioStop, isCached, resourceType, resourceUrl, getResourcePathSync, audioInitialized])

  const togglePlay = (e: React.MouseEvent) => {
    e.stopPropagation()

    // 如果音频尚未初始化，先初始化
    if (!audioInitialized) {
      const audio = initializeAudio()
      if (!audio) return
    }

    if (!audioRef.current) return

    if (isPlaying) {
      audioRef.current.pause()
      setIsPlaying(false)
    } else {
      // 使用音频管理器播放
      AudioManager.play(audioRef.current, id)
      setIsPlaying(true)
    }
  }

  // 处理拖拽开始事件
  const handleDragStart = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    if (!resourceType || !resourceUrl ) return

    // 设置拖拽数据
    const dragData = {
      resourceType,
      resourceUrl,
      id,
      title,
      durationMsec: durationMsec || (audioRef.current?.duration ? Math.round(audioRef.current.duration * 1000) : 10000),
      customExt,
    }

    e.dataTransfer.setData('application/json', JSON.stringify(dragData))

    e.dataTransfer.effectAllowed = 'copy'

    if (thumbnailUrl) {
      const img = new Image()
      img.src = thumbnailUrl
      e.dataTransfer.setDragImage(img, 25, 25)
    }
  }, [resourceType, resourceUrl, id, title, durationMsec, thumbnailUrl, customExt])

  const cacheIndicator = resourceType && resourceUrl && (
    <div className="absolute right-0 bottom-0">
      <ResourceCacheIndicator
        resourceType={resourceType}
        resourceUrl={resourceUrl}
        isLoading={isLoading}
        size={12}
        onDownload={onAdd}
      />
    </div>
  )

  // const playButton = audioUrl && (
  //   <button
  //     onClick={togglePlay}
  //     className="
  //       absolute
  //       top-0
  //       right-0
  //       rounded
  //       cursor-pointer
  //       text-gray-500
  //       hover:text-white
  //       hover:bg-gray-600
  //       p-1
  //       transition-all
  //       z-10
  //       opacity-0 group-hover:opacity-100
  //     "
  //   >
  //     {isPlaying ? <PauseIcon size={16} /> : <PlayIcon size={16} />}
  //   </button>
  // )

  // 处理点击事件，默认为播放功能
  const handleClick = useCallback((e: React.MouseEvent) => {
    if (onClick) {
      // 如果提供了onClick回调，则调用它
      onClick()
    } else if (audioUrl) {
      // 否则默认为播放/暂停功能
      togglePlay(e)
    }
  }, [onClick, audioUrl, togglePlay])

  useEffect(() => {
    return () => {
      if (audioRef.current) {
        audioRef.current.pause()

        audioRef.current.src = ''
        audioRef.current.load()

        // 移除事件监听器
        audioRef.current.removeEventListener('timeupdate', handleTimeUpdate)
        audioRef.current.removeEventListener('ended', handleAudioEnded)
        audioRef.current.removeEventListener('error', handleAudioError)

        // 移除全局事件监听器
        window.removeEventListener('audiomanager:stopped', handleGlobalAudioStop)

        // 如果这个音频正在全局播放，从管理器中移除
        if (AudioManager.currentId === id) {
          AudioManager.currentAudio = null
          AudioManager.currentId = null
        }
      }
    }
  }, [handleTimeUpdate, handleAudioEnded, handleAudioError, handleGlobalAudioStop, id])

  return (
    <div
      className={`aspect-square ${className}`}
      onClick={handleClick}
      draggable={!!resourceType && !!resourceUrl }
      onDragStart={handleDragStart}
    >
      <div
        className={cn(
          `group relative w-full h-full
          bg-muted/30
          rounded  dark:bg-gray-800/40
          border  dark:border-gray-700/10
          hover:border-blue-500/20 dark:hover:border-blue-500/20
          hover:bg-blue-500/5 dark:hover:bg-blue-500/5
          transition-all overflow-hidden`,
          isCached ? 'border-green-500/20 dark:border-green-500/20' : '',
          resourceType && resourceUrl ? 'cursor-grab active:cursor-grabbing' : ''
        )}
      >
        {
          description && (
            <div className="text-[10px] border border-gray-500 bg-gray-500/20 rounded px-1 h-4 flex items-center justify-center text-gray-500 absolute bottom-1 left-1 z-10">
              {description}
            </div>
          )
        }

        {thumbnailUrl ? (
          <div className="absolute inset-0 flex items-center justify-center">
            {imageLoadError ? (
              <div className="flex items-center justify-center text-gray-400">
                <ImageOff className="w-8 h-8" />
              </div>
            ) : (
              <img
                src={thumbnailUrl}
                alt={title}
                className="max-w-full max-h-full object-contain"
                loading="lazy"
                onError={() => setImageLoadError(true)}
                onLoad={() => setImageLoadError(false)}
              />
            )}
          </div>
        ) : icon && (
          <div className="absolute inset-0 flex items-center justify-center text-gray-400">
            {icon}
          </div>
        )}

        {/* 进度条 */}
        {isPlaying && (
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-200 dark:bg-gray-700">
            <div
              className="h-full bg-blue-500 transition-all duration-100"
              style={{ width: `${progress}%` }}
            />
          </div>
        )}

        {/* {playButton} */}
        {cacheIndicator}
        {collectionIndicator}
      </div>
      {children}
      {
        title && (
          <div className="text-xs w-full text-neutral-300 truncate mt-2">
            {title}
          </div>
        )
      }
    </div>
  )
}
