import React from 'react'
import { Camera } from 'lucide-react'
import { OverlayType } from '@app/rve-shared/types'
import { registerMaterialPlugin } from '../registry'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'

const Panel = React.lazy(() =>
  import('@rve/editor/components/plugin-panels/script.tsx')
)

export default registerMaterialPlugin({
  id: ResourceType.SCRIPT,
  title: '脚本',
  icon: Camera,
  component: Panel,
  overlayType: OverlayType.SOUND,
  order: 99,
})
