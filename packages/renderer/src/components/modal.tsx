import { Loader2 } from 'lucide-react'
import { But<PERSON> } from './ui/button'
import React, { ReactNode, useEffect } from 'react'
import { useModalContext } from '@/libs/modal'
import * as DIALOG from '@/components/ui/dialog'
import * as SHEET from '@/components/ui/sheet'

interface ModalLib {
  Title: typeof DIALOG.DialogTitle
  Header: typeof DIALOG.DialogHeader
  Description: typeof DIALOG.DialogDescription
  Close: typeof DIALOG.DialogClose
  Footer: typeof DIALOG.DialogFooter
}

const modals: Record<string, ModalLib> = {
  dialog: {
    Title: DIALOG.DialogTitle,
    Header: DIALOG.DialogHeader,
    Description: DIALOG.DialogDescription,
    Close: DIALOG.DialogClose,
    Footer: DIALOG.DialogFooter,
  },
  sheet: {
    Title: SHEET.SheetTitle as any,
    Header: SHEET.SheetHeader,
    Description: SHEET.SheetDescription as any,
    Close: SHEET.SheetClose as any,
    Footer: SHEET.SheetFooter,
  },
}

interface ModalHeaderProps {
  title: string
  description?: string
}

export function ModalHeader({ title, description }: ModalHeaderProps) {
  const { variant } = useModalContext()
  const { Header, Title, Description } = modals[variant]

  return (
    <Header>
      <Title>{title}</Title>
      {description && <Description>{description}</Description>}
    </Header>
  )
}
export interface CustomButton {
  label: string
  onClick: () => void
}
interface ModalFooterProps {
  pending?: boolean
  type?: 'submit' | 'button'
  onCancel?: () => void
  onConfirm?: () => void
  children?: ReactNode
  customButtons?: CustomButton[]
}

export function ModalFooter({
  pending = false,
  type = 'submit',
  onCancel,
  onConfirm,
  children,
  customButtons = [],
}: ModalFooterProps) {
  const { closed, variant } = useModalContext()
  const { Footer, Close } = modals[variant]

  useEffect(() => {
    if (!closed) return
    onCancel?.()
  }, [closed, onCancel])

  const showDefaultButtons = customButtons.length === 0

  return (
    <Footer className="items-center mt-auto">
      <div className="mr-auto flex items-center">{children}</div>
      {showDefaultButtons ? (
        <>
          <Close asChild>
            <Button variant="outline" type="button" className="w-[60px] h-8 bg-white/5">
              取消
            </Button>
          </Close>
          <Button
            variant="default"
            className="w-[60px] h-8 bg-primary-highlight1 hover:bg-primary-highlight1/80 text-white"
            type={type}
            onClick={onConfirm}
          >
            {pending ? <Loader2 className="animate-spin size-4" /> : '确定'}
          </Button>{' '}
        </>
      ) : (
        customButtons.map((btn, idx) => (
          <Button
            key={idx}
            variant="default"
            className="min-w-[80px] h-8 ml-2 bg-white/5 text-white border hover:bg-primary-highlight1"
            type={type}
            onClick={btn.onClick}
          >
            {pending ? <Loader2 className="animate-spin size-4" /> : btn.label}
          </Button>
        ))
      )}
    </Footer>
  )
}

interface ModalContentProps extends ModalHeaderProps, ModalFooterProps {
  children?: ReactNode
}

export function ModalContent({ children, ...props }: ModalContentProps) {
  return (
    <>
      <DIALOG.DialogContent className="bg-gradient-to-tr from-[#101C2C] to-[#002B48] [background-image:linear-gradient(to_top_right,#101C2C, #002B48_70%)] p-6">
        <ModalHeader {...props} />
        {children}
        <ModalFooter {...props} />
      </DIALOG.DialogContent>
    </>
  )
}
