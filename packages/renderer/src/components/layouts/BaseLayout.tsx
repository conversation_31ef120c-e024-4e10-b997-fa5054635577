import clsx from 'clsx'
import React, { useEffect, useState } from 'react'
import { NavLink, Outlet } from 'react-router-dom'
import type { MenuItem } from './HomeLayout'
import { Moon, Sun } from 'lucide-react'
import { UserMenu } from '@/components/auth/UserMenu'

interface BaseLayoutProps {
  menuItems: MenuItem[]
  sidebarClassName?: string
}

const DarkModeToggleButton = () => {
  const [darkMode, setDarkMode] = useState(true) // 默认使用暗色主题

  // 切换主题
  const toggleDarkMode = () => {
    const newDarkMode = !darkMode
    setDarkMode(newDarkMode)

    // 保存设置到本地存储
    localStorage.setItem('theme', newDarkMode ? 'dark' : 'light')

    // 应用主题
    if (newDarkMode) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }

  // 初始化暗色主题
  useEffect(() => {
    // 从本地存储中获取主题设置，如果没有则默认使用暗色
    const savedTheme = localStorage.getItem('theme')
    const isDark = savedTheme ? savedTheme === 'dark' : true

    setDarkMode(isDark)

    // 应用主题
    if (isDark) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }, [])

  // {/* 日夜模式切换按钮 */}
  return (
    <button
      className="p-2 rounded hover:bg-neutral-200 dark:hover:bg-neutral-700"
      onClick={toggleDarkMode}
      title={darkMode ? '切换到亮色模式' : '切换到暗色模式'}
    >
      {darkMode ? <Sun /> :  <Moon /> }
    </button>
  )
}

const HeaderToolbar = () => {
  return (
    <div className=" h-14 px-6 flex items-center justify-end border-b border-neutral-700">
      {/* 左侧 - 用户信息 */}
      <div className="flex items-center space-x-8">
        <div className="flex gap-3 items-center">
          <DarkModeToggleButton />

          {/*<button*/}
          {/*  className="p-2 rounded hover:bg-neutral-200 dark:hover:bg-neutral-700"*/}
          {/*  onClick={handleAddSettings}*/}
          {/*  title="设置"*/}
          {/*>*/}
          {/*  <Settings2 />*/}
          {/*</button>*/}
        </div>

        {/* 用户菜单 - 包含个人设置和退出登录功能 */}
        <UserMenu />
      </div>
    </div>
  )
}

export const BaseLayout: React.FC<BaseLayoutProps> = ({ menuItems, sidebarClassName }) => {
  return (
    <div
      className={`
        flex flex-col w-full h-full bg-cover bg-center bg-no-repeat
        [background-image:url(/images/bg-dashboard.jpg)]
        dark:[background-image:url(/images/bg-dark-dashboard.jpg)]
      `}
    >
      <HeaderToolbar />

      <div className="flex-1 flex overflow-hidden py-6 px-3 gap-4">
        {/* 侧边栏 */}
        <aside className={clsx(' transition-all duration-300 w-50  ', sidebarClassName)}>
          {/* 菜单区域 */}
          <nav className="ml-6 mt-6">
            <div className="flex flex-col gap-2">
              {menuItems.map(item => (
                <div key={item.key} className="h-10">
                  <NavLink
                    to={item.path}
                    className={({ isActive }) => clsx(
                      'flex items-center px-3 py-2 h-full rounded-xl transition-colors text-sm',
                      isActive
                        ? 'bg-gradient-brand  text-black font-bold'
                        : 'hover:bg-neutral-700'
                    )}
                  >
                    <span className="mr-3">{item.icon}</span>
                    <span>{item.title}</span>
                  </NavLink>
                </div>
              ))}
            </div>
          </nav>
        </aside>

        {/* 主内容区域 */}
        <div className="flex flex-col flex-1 overflow-hidden ">
          {/* 内容区域 - 滚动容器 */}
          <main className="flex-1 overflow-hidden ">
            <Outlet />
          </main>
        </div>
      </div>
    </div>
  )
}
