import React, { ReactNode, useMemo } from 'react'
import { UploadIcon, PlusIcon, FolderUpIcon } from 'lucide-react'
import { Button } from './ui/button'
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover'
import { FileUploader, FileUploaderRenderProps, UploadedFile } from '@/components/ui/file-uploader'
import { FolderUploader } from '@/components/ui/folder-uploader'
import { getPathChain, TreeNode } from '@/components/TreeList'
import { cn } from '@/components/lib/utils'

export type Folder = {
  id: string
  name: string
}

export type LocalResourceItem = {
  id: string
  type: string
  thumbnail?: string
  name: string
  path?: string
}

export interface LocalResourcePanelProps {
  /**
   * 目录数据
   */
  dirList: TreeNode[]
  /**
   * 当前选中的目录
   */
  currentFolderId: string
  /**
   * 切换目录回调
   */
  onFolderChange: (folderId: string) => void // 新增: 切换目录回调
  /**
   * 创建文件夹
   */
  onCreateFolder: () => void
  /**
   * 文件上传类型
   */
  fileUploadTypes: string[]
  /**
   * 关键词
   */
  searchKey?: string
  /**
   * 上传完成回调
   */
  onUploadComplete?: (uploaded: UploadedFile[], folderId: string) => void
  /**
   * 资源列表
   */
  resources?: any
  /**
   * 是否显示上传按钮
   */
  showUpload?: boolean
  /**
   * 是否显示新建文件夹按钮
   */
  showCreateFolder?: boolean
  /**
   * 列表为空时的提示文本
   */
  emptyText?: string
  /**
   * 列表容器的类名
   */
  containerClassName?: string
  /**
   * 资源网格的列数
   */
  gridCols?: number
  /**
   * 是否加载中
   */
  isLoading?: boolean
  /**
   * 资源项渲染函数
   */
  renderResourceItem?: (resource: LocalResourceItem, index: number) => ReactNode
}
const CustomUploader: React.FC<FileUploaderRenderProps> = ({
  getRootProps,
  getInputProps,
  isLoading,
}) => {
  return (
    <div className="flex items-center justify-center gap-2">
      <UploadIcon className="w-3.5 h-3.5" />
      <div {...getRootProps()}>
        <input {...getInputProps()} />
        {isLoading ? '上传中...' : '上传文件'}
      </div>
    </div>
  )
}
/**
 * 通用本地资源面板组件
 * 提供文件夹选择、上传文件、新建文件夹功能，以及资源列表展示
 */
export function LocalResourcePanel({
  dirList,
  currentFolderId,
  onFolderChange,
  onCreateFolder,
  fileUploadTypes,
  searchKey = '',
  onUploadComplete,
  resources = [],
  showUpload = true,
  showCreateFolder = true,
  renderResourceItem,
  emptyText = '暂无资源',
  containerClassName = '',
  gridCols = 4,
  isLoading = false,
}: LocalResourcePanelProps) {
  // 获取目录链
  const folderPath = useMemo(() => {
    if (!currentFolderId || !dirList) return []
    return getPathChain(dirList, currentFolderId) ?? []
  }, [currentFolderId, dirList])

  //获得当前目录下的子目录
  const childFolders = useMemo(() => {
    if (!dirList) return []

    const findChildren = (nodes: any[]): any[] => {
      for (const node of nodes) {
        if (String(node.id) === String(currentFolderId)) {
          return node.children || []
        }

        if (node.children && node.children.length > 0) {
          const result = findChildren(node.children)
          if (result.length > 0) {
            return result
          }
        }
      }
      return []
    }
    let children = findChildren(dirList)

    if (searchKey && searchKey !== '') {
      const keyword = searchKey.toLowerCase()
      children = children.filter(child => child.label?.toLowerCase().includes(keyword))
    }

    return children
  }, [dirList, currentFolderId, searchKey])

  //上传完成之后创建本地资源
  const handleUploadComplete = async (files: UploadedFile[]) => {
    const uploaded = files.filter(f => f.status === 'success')
    console.log('上传完成信息:', uploaded)
    if (onUploadComplete) {
      onUploadComplete(uploaded, currentFolderId)
    }
  }

  // 处理资源点击
  const onResourceClick = (resource: LocalResourceItem) => {
    console.log('点击资源', resource)
  }

  // 处理资源添加到时间轴
  const onResourceAdd = (resource: LocalResourceItem) => {
    console.log('添加资源到时间轴', resource)
  }

  // 默认的资源项渲染函数
  const defaultRenderResourceItem = (resource: LocalResourceItem, index: number) => {
    return (
      <div
        key={resource.id || index}
        className="aspect-square bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
        onClick={() => onResourceClick?.(resource)}
        onDoubleClick={() => onResourceAdd?.(resource)}
      >
        {resource.thumbnail ? (
          <img
            src={resource.thumbnail}
            alt={resource.name}
            className="w-full h-full object-cover rounded-lg"
          />
        ) : (
          <div className="text-gray-400 flex flex-col items-center justify-center p-2">
            <span className="text-xs text-center mt-1 truncate w-full">{resource.name}</span>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={`flex flex-col gap-4 mt-2 ${containerClassName}`}>
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2 w-full justify-between">
          {showUpload && (
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center gap-1">
                  <UploadIcon className="w-3.5 h-3.5" />
                  <span>上传</span>
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-36 p-0">
                <div className="py-1">
                  <button className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-800 flex items-center gap-2">
                    <FileUploader
                      fileTypes={fileUploadTypes}
                      folderUuid={currentFolderId}
                      onUpload={handleUploadComplete}
                      renderCustomComponent={props => <CustomUploader {...props} />}
                    />
                  </button>
                  <button className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-800 flex items-center gap-2">
                    <FolderUploader
                      folderUuid={currentFolderId}
                      children={
                        <div className="flex items-center justify-center">
                          <FolderUpIcon className="w-3.5 h-3.5 mr-2" />
                          上传文件夹
                        </div>
                      }
                      isShowUploadedFiles={false}
                      showFileList={false}
                      onProgress={(current, total) => {
                        console.log({ current, total })
                      }}
                      onUpload={async () => {
                        // 刷新列表
                        // await queryClient.invalidateQueries({
                        //   queryKey: [QUERY_KEYS.MATERIAL_DIRECTORY_LIST],
                        // })
                      }}
                    />
                  </button>
                </div>
              </PopoverContent>
            </Popover>
          )}
          {showCreateFolder && (
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
              onClick={onCreateFolder}
            >
              <PlusIcon className="w-3.5 h-3.5" />
              <span>新建文件夹</span>
            </Button>
          )}
        </div>
      </div>
      <div>
        <div className="flex items-center space-x-1 text-white">
          {folderPath.map((folder, index) => (
            <React.Fragment key={folder.id}>
              <button
                className={cn('hover:underline', {
                  'text-primary-highlight1': folder.id === currentFolderId,
                })}
                onClick={() => onFolderChange(folder.id)}
              >
                {folder.label}
              </button>
              {index < folderPath.length - 1 && <span>{'>'}</span>}
            </React.Fragment>
          ))}
        </div>
      </div>
      {/* 资源列表区域 */}
      {isLoading ? (
        <div className="flex justify-center items-center h-40">
          <div className="text-gray-400">加载中...</div>
        </div>
      ) : (
        <>
          {/* 先渲染子文件夹 */}
          {childFolders.length > 0 && (
            <div className={`grid grid-cols-${gridCols} gap-3 mb-4`}>
              {childFolders.map(folder => (
                <div
                  key={folder.id}
                  className="aspect-square bg-blue-100 dark:bg-blue-800 rounded-lg flex items-center justify-center cursor-pointer hover:bg-blue-200 dark:hover:bg-blue-700 transition-colors"
                  onClick={() => onFolderChange(folder.id)}
                >
                  <span className="text-sm truncate">{folder.label}</span>
                </div>
              ))}
            </div>
          )}

          {/* 再渲染资源 */}
          {resources.length > 0 ? (
            <div className={`grid grid-cols-${gridCols} gap-3`}>
              {resources.map((resource, index) =>
                renderResourceItem
                  ? renderResourceItem(resource, index)
                  : defaultRenderResourceItem(resource, index),
              )}
            </div>
          ) : childFolders.length === 0 ? (
            <div className="flex justify-center items-center h-40">
              <div className="text-gray-400">{emptyText}</div>
            </div>
          ) : null}
        </>
      )}
    </div>
  )
}

export default LocalResourcePanel
