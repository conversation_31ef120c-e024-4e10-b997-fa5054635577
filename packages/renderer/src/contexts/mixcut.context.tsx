import React, { PropsWithChildren, useCallback, useMemo, useState } from 'react'
import { OverlayType, RenderableOverlay, TrackType, VideoOverlay } from '@app/rve-shared/types'
import { Combo, RenderRequestPayload } from '@app/shared/types/ipc/editor.ts'
import { calculateDuration, calculateRenderableOverlays } from '@rve/editor/utils/overlay-helper.ts'
import { queryFirstKeyframeOfVideo } from '@/hooks/queries/useQueryVideoKeyframe.ts'
import { toast } from 'react-toastify'
import { queryClient } from '@/main.tsx'
import { QUERY_KEYS } from '@/constants/queryKeys.ts'

import { EditorState } from '@/libs/cache/parts/editor.cache.ts'
import { useVirtualTab } from '@/contexts'
import { sleep } from '@app/shared/utils.ts'
import { useQuery } from '@tanstack/react-query'
import { EditorModule } from '@/libs/request/api/editor.ts'
import { Mixcut } from '@/types/mixcut.ts'

// 混剪素材规则
type MixcutMaterialRule = {
  // 启用随机打乱. 开启后,将指定分镜打乱顺序进行排列组合
  enableShuffle: boolean

  // *分镜素材智能截取:
  // 开启后，系统将分镜里的素材,如分镜时长5s，放入10秒的素材，将从10s的素材中智能随机选取5秒;若不开启则默认从头截取5秒
  enableSmartTrim: boolean
}

type MixcutPageTabs = 'generation' | 'saved'

type DeduplicateOperations =
  | 'rotate'            // 随机旋转
  | 'scale'             // 随机缩放
  | 'offset'            // 随机偏移
  | 'caption-position'  // 随机字幕位置
  | 'video-start'       // 随机视频起始点
  | 'speed'             // 随机变速
  | 'trim-end'          // 随机去片尾
  | 'mask'              // 随机蒙版

// 视频去重规则
type VideoDeduplicateRule = {
  // 启用视频去重
  enableDeduplicate: boolean
  operationStatus: Map<DeduplicateOperations, boolean>
}

export type MultiSelection<TData = any> = {
  list: TData[]

  activeIndex: number | null
  setActiveIndex(v: number | null): void
  activeItem: TData | null

  selectedIndices: Set<number>
  setSelectedIndices: React.Dispatch<React.SetStateAction<Set<number>>>
  toggleSelection: (index: number) => void
  clearSelection: () => void
  selectAll: () => void
}

function useMultiSelection<TData = any>(datasource: TData[]): MultiSelection<TData> {
  const [activeIndex, setActiveIndex] = useState<number | null>(null)
  const [selectedIndices, setSelectedIndices] = useState<Set<number>>(new Set())

  // 多选相关方法
  const toggleSelection = useCallback((index: number) => {
    setSelectedIndices(prev => {
      const newSet = new Set(prev)
      if (newSet.has(index)) {
        newSet.delete(index)
      } else {
        newSet.add(index)
      }
      return newSet
    })
  }, [])

  const clearSelection = useCallback(() => {
    setSelectedIndices(new Set())
  }, [])

  const selectAll = useCallback(() => {
    setSelectedIndices(new Set(datasource.map((_, index) => index)))
  }, [datasource])

  const activeItem = useMemo(
    () => activeIndex !== null ? datasource[activeIndex] : null,
    [activeIndex, datasource]
  )

  return {
    list: datasource,
    activeIndex,
    setActiveIndex,
    activeItem,

    selectedIndices,
    setSelectedIndices,
    toggleSelection,
    clearSelection,
    selectAll,
  }
}

export type MixcutContextValues = {
  state: EditorState
  activeTab: MixcutPageTabs
  setActiveTab(v: MixcutPageTabs): void

  generation: MultiSelection<GeneratedMixcut> & {
    batchUploadState: {
      visible: boolean
      completed: number
      total: number
    }
    /**
     * 批量上传选中的混剪结果
     */
    uploadSelectedPreviews: () => Promise<void>

    /**
     * 生成所有可能的混剪组合
     */
    generateCombinations: () => void
  }

  saved: MultiSelection<Mixcut.SavedMixcut> & {
  }

  playerOverlays: RenderableOverlay[]

  rules: {
    material: MixcutMaterialRule,
    deduplicate: VideoDeduplicateRule,
  }
}

export type GeneratedMixcut = Combo & {
  cover?: string
}

export const MixcutContext = React.createContext<MixcutContextValues>(null as any)

const useGenerationPart = (state: EditorState, scriptId: string) => {
  const [limit] = useState(10)
  const [generatedMixcuts, setGeneratedMixcuts] = useState<GeneratedMixcut[]>([])
  const [batchUploadState, setBatchUploadState] = useState<MixcutContextValues['generation']['batchUploadState']>({
    visible: false,
    completed: 0,
    total: 0,
  })

  const multiSelection = useMultiSelection(generatedMixcuts)

  // 批量上传方法
  const uploadSelectedPreviews = useCallback(
    async () => {
      const { selectedIndices, setSelectedIndices } = multiSelection

      const selectedIndicesArray = Array.from(selectedIndices)
      if (selectedIndicesArray.length === 0) {
        toast.warning('请先选择要上传的混剪结果')
        return
      }

      setBatchUploadState({
        visible: true,
        completed: 0,
        total: selectedIndicesArray.length,
      })

      try {
        for (let i = 0; i < selectedIndicesArray.length; i++) {
          const index = selectedIndicesArray[i]
          const combo = generatedMixcuts[index]

          setBatchUploadState(prev => ({
            ...prev,
            currentItem: `组合 [${combo.combination.join(', ')}]`
          }))

          // 获取第一个视频overlay用于封面
          const { tracks, playerMetadata } = state
          const firstStoryboardTrackIndex = combo.combination[0]
          const targetVideoTrack = tracks[firstStoryboardTrackIndex]
          const firstVideoOverlay = targetVideoTrack?.overlays.find(
            (overlay: any) => overlay.storyboardIndex === 0 && overlay.type === OverlayType.VIDEO
          ) as VideoOverlay | null

          const overlays = calculateRenderableOverlays(tracks, combo.combination)

          const data: RenderRequestPayload = {
            id: 'V0-0-0',
            inputProps: {
              overlays,
              playerMetadata
            }
          }

          // 调用上传方法
          await Promise.all([
            window.editor.uploadMixcutResult({
              scriptId,
              data,
              similarity: combo.similarity,
              cover: await queryFirstKeyframeOfVideo(firstVideoOverlay?.src) || undefined,
              duration: calculateDuration(overlays)
            }),
            new Promise(resolve => setTimeout(resolve, 1500))
          ])

          setBatchUploadState(prev => ({
            ...prev,
            completed: i + 1
          }))
        }

        toast.success(`成功保存 ${selectedIndicesArray.length} 个混剪结果！`)

        setGeneratedMixcuts(prev => {
          return prev.filter((_, index) => !selectedIndicesArray.includes(index))
        })
        setSelectedIndices(new Set())

        void queryClient.refetchQueries({ queryKey: [QUERY_KEYS.SAVED_MIXCUT_LIST] })
        await sleep(1000)
      } catch (error) {
        console.error('批量上传失败:', error)
        toast.error(`批量上传失败: ${error instanceof Error ? error.message : '未知错误'}`)
      } finally {
        setBatchUploadState({
          visible: false,
          completed: 0,
          total: 0,
        })
      }
    },
    [multiSelection, generatedMixcuts, state]
  )

  const generateCombinations = useCallback(async () => {
    const { tracks } = state

    const storyboardTrack = tracks.find(track => track.type === TrackType.STORYBOARD)
    if (!storyboardTrack) {
      return []
    }

    const storyboards = storyboardTrack.overlays
    const videoTracks = tracks
      .filter(t => t.type === TrackType.VIDEO && !t.isGlobalTrack)
      .map((track, index) => ({ ...track, index: index + 1 }))

    const matrix = storyboards
      .map((_, storyboardIndex) => {
        return videoTracks
          .filter(t => t.overlays.some(o => o.storyboardIndex === storyboardIndex))
          .map(o => o.index)
      })

    setGeneratedMixcuts(
      await window.editor.generateCombos({
        limit,
        threshold: 1,
        matrix,
      })
    )
    // 清空选择状态
    multiSelection.setSelectedIndices(new Set())
  }, [state.tracks, limit])

  return {
    generatedMixcuts,
    batchUploadState,
    uploadSelectedPreviews,
    generateCombinations,

    ...multiSelection,
  }
}

const useSavedPart = (_state: EditorState, scriptId: string) => {
  const { data } = useQuery({
    queryKey: [QUERY_KEYS.SAVED_MIXCUT_LIST, scriptId],
    queryFn: () => EditorModule.listMixcuts(scriptId)
  })

  const multiSelection = useMultiSelection(data?.list || [])

  return {
    ...multiSelection,
  }
}

export const MixcutProvider: React.FC<PropsWithChildren<{ state: EditorState }>> = ({
  children, state
}) => {
  const { params } = useVirtualTab()
  const scriptId = params?.scriptId

  const [activeTab, setActiveTab] = useState<MixcutPageTabs>('generation')

  const generationPart = useGenerationPart(state, scriptId)
  const savedPart = useSavedPart(state, scriptId)

  const playerOverlays = useMemo(() => {
    if (activeTab === 'generation' && generationPart.activeItem) {
      return calculateRenderableOverlays(state.tracks, generationPart.activeItem.combination)
    }

    return []
  }, [activeTab, state.tracks, generationPart.activeItem, savedPart.activeItem])

  return (
    <MixcutContext.Provider
      value={{
        state,

        generation: generationPart,
        saved: savedPart,

        playerOverlays,
        activeTab,
        setActiveTab,
        rules: {
          material: {
            enableShuffle: false,
            enableSmartTrim: false
          },
          deduplicate: {
            enableDeduplicate: false,
            operationStatus: new Map()
          }
        },
      }}
    >
      {children}
    </MixcutContext.Provider>
  )
}

export const useMixcutContext = () => {
  const context = React.useContext(MixcutContext)
  if (!context) {
    throw new Error('useMixcutContext must be used within a MixcutProvider')
  }
  return context
}
