import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { Slide, ToastContainer } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'
import errorService from '@/services/ErrorService'
import { ErrorInfo, ErrorHandlerOptions, ErrorType } from '@/types/error'

interface ErrorContextType {
  handleError: (error: unknown, options?: ErrorHandlerOptions) => ErrorInfo
  handleBusinessError: (code: number, options?: ErrorHandlerOptions) => ErrorInfo
  createError: (
    message: string,
    code: string | number,
    type?: ErrorType,
    options?: ErrorHandlerOptions
  ) => ErrorInfo
  // 最近的错误
  lastError: ErrorInfo | null
  // 清除最近的错误
  clearLastError: () => void
}

export const ErrorHandlerContext = createContext<ErrorContextType | undefined>(undefined)

interface ErrorProviderProps {
  children: ReactNode
}

export const ErrorHandlerProvider: React.FC<ErrorProviderProps> = ({ children }) => {
  const [lastError, setLastError] = useState<ErrorInfo | null>(null)

  const handleError = (error: unknown, options?: ErrorHandlerOptions): ErrorInfo => {
    const errorInfo = errorService.handleError(error, options)
    setLastError(errorInfo)
    return errorInfo
  }

  const handleBusinessError = (code: number, options?: ErrorHandlerOptions): ErrorInfo => {
    const errorInfo = errorService.handleBusinessError(code, options)
    setLastError(errorInfo)
    return errorInfo
  }

  const createError = (
    message: string,
    code: string | number,
    type: ErrorType = ErrorType.UNKNOWN,
    options?: ErrorHandlerOptions
  ): ErrorInfo => {
    const errorInfo = errorService.createErrorInfo(message, code, type, options)

    // 处理错误（显示提示、上报）
    handleError(errorInfo)

    return errorInfo
  }

  // 清除最近的错误
  const clearLastError = () => {
    setLastError(null)
  }

  // 安装全局错误处理器
  useEffect(() => {
    errorService.installGlobalHandlers()
  }, [])

  const contextValue: ErrorContextType = {
    handleError,
    handleBusinessError,
    createError,
    lastError,
    clearLastError
  }

  return (
    <ErrorHandlerContext.Provider value={contextValue}>
      {children}
      <ToastContainer
        position="top-center"
        autoClose={3000}
        newestOnTop
        closeOnClick
        rtl={false}
        draggable
        pauseOnHover={false}
        theme="dark"
        hideProgressBar
        transition={Slide}

      />
    </ErrorHandlerContext.Provider>
  )
}

export const useErrorHandler = (): ErrorContextType => {
  const context = useContext(ErrorHandlerContext)

  if (context === undefined) {
    throw new Error('useError must be used within an ErrorProvider')
  }

  return context
}

// 错误边界组件
interface ErrorBoundaryProps {
  children: ReactNode
  fallback?: ReactNode | ((error: ErrorInfo) => ReactNode)
}

interface ErrorBoundaryState {
  hasError: boolean
  errorInfo: ErrorInfo | null
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {

  static contextType = ErrorHandlerContext
  declare context: React.ContextType<typeof ErrorHandlerContext>

  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = {
      hasError: false,
      errorInfo: null
    }
  }

  static getDerivedStateFromError(_error: unknown): ErrorBoundaryState {
    // 更新状态，下一次渲染将显示回退 UI
    return {
      hasError: true,
      errorInfo: null // 在 componentDidCatch 中设置
    }
  }

  componentDidCatch(error: Error, reactErrorInfo: React.ErrorInfo): void {
    // 使用错误服务处理错误
    if (this.context) {
      const errorInfo = this.context.handleError(error, {
        context: {
          componentStack: reactErrorInfo.componentStack
        }
      })

      this.setState({
        errorInfo
      })
    }
  }

  // render(): ReactNode {
  //   const { hasError, errorInfo } = this.state
  //   const { children, fallback } = this.props
  //
  //   if (hasError && errorInfo) {
  //     // 如果提供了回退 UI，则渲染它
  //     if (fallback) {
  //       if (typeof fallback === 'function') {
  //         return fallback(errorInfo)
  //       }
  //       return fallback
  //     }
  //
  //     // 默认回退 UI
  //     return (
  //       <div className="error-boundary-fallback">
  //         <h2>出错了</h2>
  //         <p>{errorInfo.message}</p>
  //         <button onClick={() => this.setState({ hasError: false, errorInfo: null })}>
  //           重试
  //         </button>
  //       </div>
  //     )
  //   }
  //
  //   return children
  // }
}
