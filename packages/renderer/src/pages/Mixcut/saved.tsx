import { useMixcutContext, useVirtualTab } from '@/contexts'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys.ts'
import { EditorModule } from '@/libs/request/api/editor.ts'
import React, { useCallback, useState } from 'react'
import { toast } from 'react-toastify'
import { Mixcut } from '@/types/mixcut.ts'
import { Button } from '@/components/ui/button.tsx'
import { Download, Trash2 } from 'lucide-react'
import { WithConfirm } from '@/components/WithConfirm.tsx'
import { clsx } from 'clsx'
import { MultiSelectableCard } from './common'
import { OssModule } from '@/libs/request/api/oss.ts'
import { RenderRequestPayload } from '@app/shared/types/ipc/editor.ts'

// 导出进度遮罩组件
const ExportProgressOverlay: React.FC<{
  exportState: {
    visible: boolean
    completed: number
    total: number
    currentItem: string
  }
}> = ({ exportState }) => {
  const { visible, completed, total, currentItem } = exportState

  if (!visible) return null

  const progress = total > 0 ? (completed / total) * 100 : 0

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-background rounded-lg p-8 shadow-lg border max-w-sm w-full mx-4">
        <div className="flex flex-col items-center space-y-6">
          {/* 圆环进度条 */}
          <div className="relative w-32 h-32">
            <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
              {/* 背景圆环 */}
              <circle
                cx="60"
                cy="60"
                r="54"
                stroke="oklch(0.551 0.027 264.364)"
                strokeWidth="8"
                fill="transparent"
              />
              {/* 进度圆环 */}
              <circle
                cx="60"
                cy="60"
                r="54"
                stroke="oklch(0.715 0.143 215.221)"
                strokeWidth="8"
                fill="transparent"
                strokeDasharray={`${2 * Math.PI * 54}`}
                strokeDashoffset={`${2 * Math.PI * 54 * (1 - progress / 100)}`}
                className="transition-all duration-300 ease-in-out"
              />
            </svg>
            {/* 中心文字 */}
            <div className="absolute inset-0 flex flex-col items-center justify-center">
              <div className="text-2xl font-bold text-foreground">
                {completed}
              </div>
              <div className="text-sm text-muted-foreground">
                / {total}
              </div>
            </div>
          </div>

          {/* 状态文本 */}
          <div className="text-center space-y-2">
            <h3 className="text-lg font-medium text-foreground">
              正在导出混剪
            </h3>
            {currentItem && (
              <p className="text-sm text-muted-foreground">
                当前: {currentItem}
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

// 删除所选混剪按钮组件
export const SavedMixcutDeleteButton = () => {
  const { saved } = useMixcutContext()
  const { params } = useVirtualTab()
  const { scriptId } = params || {}
  const queryClient = useQueryClient()

  const handleDeleteSelected = useCallback(async () => {
    const selectedIndicesArray = Array.from(saved.selectedIndices)
    if (selectedIndicesArray.length === 0) {
      toast.warning('请先选择要删除的混剪')
      return
    }

    if (!saved.list) {
      toast.error('获取混剪列表失败')
      return
    }

    try {
      // 获取选中混剪的 ID
      const selectedMixcuts = selectedIndicesArray.map(index => saved.list[index])
      const selectedIds = selectedMixcuts.map(mixcut => mixcut.id.toString())

      // 调用删除接口
      await EditorModule.deleteSavedMixcut({ ids: selectedIds })

      // 刷新列表
      await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.SAVED_MIXCUT_LIST, scriptId] })

      // 清空选择
      saved.clearSelection()

      toast.success(`成功删除 ${selectedIds.length} 个混剪`)
    } catch (error) {
      console.error('删除混剪失败:', error)
      toast.error('删除混剪失败，请重试')
    }
  }, [saved, scriptId, queryClient])

  return (
    <WithConfirm
      title="删除混剪"
      description={`确定要删除选中的 ${saved.selectedIndices.size} 个混剪吗？此操作不可恢复。`}
      confirmText="删除"
      confirmVariant="destructive"
      onConfirm={handleDeleteSelected}
    >
      <Button
        variant="destructive"
        size="sm"
        disabled={saved.selectedIndices.size === 0}
      >
        <Trash2 className="w-4 h-4 mr-2" />
        删除所选混剪 ({saved.selectedIndices.size})
      </Button>
    </WithConfirm>
  )
}

// 一键导出所选混剪按钮组件
export const SavedMixcutExportButton = () => {
  const { saved } = useMixcutContext()
  const [exportState, setExportState] = useState({
    visible: false,
    completed: 0,
    total: 0,
    currentItem: ''
  })

  // 从 URL 中提取 objectId
  const extractObjectId = useCallback((url: string): string | null => {
    const match = url.match(/oss\/object\/(\w+)$/)
    return match ? match[1] : null
  }, [])

  const handleExportSelected = useCallback(async () => {
    const selectedIndicesArray = Array.from(saved.selectedIndices)
    if (selectedIndicesArray.length === 0) {
      toast.warning('请先选择要导出的混剪')
      return
    }

    if (!saved.list) {
      toast.error('获取混剪列表失败')
      return
    }

    try {
      const selectedMixcuts = selectedIndicesArray.map(index => saved.list[index])

      // 显示进度遮罩
      setExportState({
        visible: true,
        completed: 0,
        total: selectedMixcuts.length,
        currentItem: ''
      })

      // 逐个处理导出
      for (let i = 0; i < selectedMixcuts.length; i++) {
        const mixcut = selectedMixcuts[i]

        setExportState(prev => ({
          ...prev,
          currentItem: mixcut.name,
          completed: i
        }))

        // 从 URL 中提取 objectId
        const objectId = extractObjectId(mixcut.url)
        if (!objectId) {
          console.error(`无法从 URL 中提取 objectId: ${mixcut.url}`)
          continue
        }

        const metadata = await OssModule.getObject<RenderRequestPayload>(objectId)

        const { inputProps: { playerMetadata: { width, height, fps, durationInFrames } } } = metadata

        // 构建 RequestRender 参数
        const renderParams: Mixcut.RequestRender = {
          objectId,
          scriptId: mixcut.scriptId,
          name: mixcut.name,
          cover: mixcut.cover,
          resolution: `${width}x${height}`,
          fps,
          duration: durationInFrames * fps,

          bitRate: 0,
          createAt: 0,
          display: 0,
          isSaveList: false,
          itemId: '1',
          previewId: 0,
          priority: 0,
          product: 0,
          repetitionRate: 0,
        }

        // 调用渲染接口
        await EditorModule.requestRender(renderParams)
      }

      // 完成所有导出
      setExportState(prev => ({
        ...prev,
        completed: selectedMixcuts.length
      }))

      toast.success(`成功提交 ${selectedMixcuts.length} 个混剪的导出任务`)

      // 延迟隐藏进度遮罩
      setTimeout(() => {
        setExportState({
          visible: false,
          completed: 0,
          total: 0,
          currentItem: ''
        })
      }, 1500)
    } catch (error) {
      console.error('导出混剪失败:', error)
      toast.error('导出混剪失败，请重试')
      setExportState({
        visible: false,
        completed: 0,
        total: 0,
        currentItem: ''
      })
    }
  }, [saved, extractObjectId])

  return (
    <>
      <Button
        variant="default"
        size="sm"
        onClick={handleExportSelected}
        disabled={saved.selectedIndices.size === 0}
      >
        <Download className="w-4 h-4 mr-2" />
        一键导出所选混剪 ({saved.selectedIndices.size})
      </Button>

      {/* 导出进度遮罩 */}
      {exportState.visible && (
        <ExportProgressOverlay exportState={exportState} />
      )}
    </>
  )
}

const SavedMixcutCard: React.FC<Mixcut.SavedMixcut & { index: number }> = ({ index: i, ...mixcut }) => {
  const { saved } = useMixcutContext()

  return (
    <MultiSelectableCard {...saved} index={i}>
      <div
        className={clsx(
          'w-48 h-64 relative outline-3 cursor-pointer ',
        )}
      >
        {/* 预览图片背景 */}
        <img
          src={mixcut.cover}
          alt={`saved-mixcut-${mixcut.id}`}
          className="w-full h-full object-cover rounded-sm"
          onError={e => {
            // 图片加载失败时显示默认背景
            e.currentTarget.style.display = 'none'
          }}
        />

        {/* 重复率标签 */}
        <div className="absolute right-[-8px] top-[-8px] bg-black/70 text-white p-1 text-xs rounded">
          重复率{(mixcut.repetitionRate * 100).toFixed(1)}%
        </div>
      </div>
    </MultiSelectableCard>
  )
}

// 左侧② - 混剪预览列表
export const SavedMixcutListPanel = () => {
  const { params } = useVirtualTab()
  const { scriptId } = params || {}
  const { data } = useQuery({
    queryKey: [QUERY_KEYS.SAVED_MIXCUT_LIST, scriptId],
    queryFn: () => EditorModule.listMixcuts(scriptId)
  })

  return (
    <div className="flex-1 h-fit flex flex-wrap gap-x-4 gap-y-6 p-4 overflow-y-auto">
      {data?.list.map((combo, i) => (
        <SavedMixcutCard
          {...combo}
          key={i}
          index={i}
        />
      ))}
    </div>
  )
}
