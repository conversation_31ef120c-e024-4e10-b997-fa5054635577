import React, { useState } from 'react'
import './login.css'
import { MessageSquareMore, Smartphone } from 'lucide-react'
import { AuthModule } from '@/libs/request/api/auth'
import { TokenManager } from '@/libs/storage'
import { useNavigate } from 'react-router'
import { z } from 'zod'

const mobileSchema = z
  .string()
  .min(11, '手机号太短')
  .max(11, '手机号太长')
  .regex(/^1[3-9]\d{9}$/, '请输入正确的手机号格式')

const codeSchema = z
  .string()
  .min(4, '请输入验证码')
  .max(6, '验证码长度不正确')
  .regex(/^\d+$/, '验证码只能包含数字')

const safe = <Fn extends (...args: any[]) => any>(fn: Fn) => {
  return (...args: Parameters<Fn>): ReturnType<Fn> | void => {
    try {
      return fn(...args)
    } catch (error) {
      console.error(error)
    }
  }
}

export default function Login() {
  const navigate = useNavigate()
  const [wait, setWait] = useState(0)
  const [code, setCode] = useState('')
  const [codeInit, setCodeInit] = useState(false)
  const [codeError, setCodeError] = useState('')
  const [mobile, setMobile] = useState('')
  const [_, setMobileInit] = useState(false)
  const [mobileError, setMobileError] = useState('')
  const [error, setError] = useState('')

  const updateWait = () => {
    const iter = (v: number) => {
      setWait(v)
      if (!v) return
      setTimeout(() => iter(v - 1), 1000)
    }
    iter(60)
  }

  const revalidMobile = (value?: string) => {
    setMobileInit(true)
    const result = mobileSchema.safeParse(value ?? mobile)
    if (result.success) {
      setMobileError('')
      return true
    }
    setMobileError(result.error.errors[0].message)
  }

  const revalidCode = (value?: string) => {
    setCodeInit(true)
    const result = codeSchema.safeParse(value ?? code)
    if (result.success) {
      setCodeError('')
      return true
    }
    setCodeError(result.error.errors[0].message)
  }

  const sendCode = safe(async () => {
    if (!revalidMobile()) return
    try {
      await AuthModule.sendSMSCode({ mobile, scene: 1 })
      updateWait()
    } catch (error: any) {
      setError(error?.message || '发送验证码失败')
    }
  })

  const login = safe(async () => {
    if (!revalidCode() || !revalidMobile()) return

    try {
      TokenManager.saveLoginData(await AuthModule.codeLogin({ mobile, code: Number(code) }))
      navigate('/home')
    } catch (error: any) {
      setError(error?.message || '登录失败')
    }
  })

  return (
    <div
      className="h-full bg-[url(/images/bg-login.jpg)]
        bg-cover bg-center bg-no-repeat relative
        before:content-[''] before:h-full before:inset-0 before:absolute before:bg-black/50
        *:relative *:z-10 flex items-center"
    >
      <div className="ml-40 text-7xl leading-25 select-none font-[ShuHeiti] font-semibold">
        <span className="text-gradient-brand">AI视频</span>智造新次元
        <br />从<span className="text-gradient-brand">创作到分发</span>，<br />
        十倍效能跃迁
      </div>
      <div className="login-box w-170 h-180 rounded-[20px] border-2 ml-60 *:blur-sm *:last:blur-none">
        <div className="size-5/6 absolute -top-[3px] -left-[3px] middle-1" />
        <div className="size-5/6 absolute -top-[4px] -left-[4px] outer-1" />
        <div className="size-5/6 absolute top-0 left-0 inner-1-1" />
        <div className="size-5/6 absolute top-0 left-0 inner-1-2" />
        <div className="size-5/6 rotate-180 absolute -bottom-[3px] -right-[3px] middle-2" />
        <div className="size-5/6 rotate-180 absolute -bottom-[4px] -right-[4px] outer-2" />
        <div className="size-5/6 rotate-180 absolute bottom-0 right-0 inner-2-1" />
        <div className="size-5/6 rotate-180 absolute bottom-0 right-0 inner-2-2" />
        <div className="login-content relative inset-0 size-full flex items-center justify-center rounded-[20px] z-10 px-30">
          <div className="flex-1">
            <div className="text-4xl">欢迎登录</div>
            <div className="flex flex-col gap-6 mt-15">
              <div className="flex-1 flex relative">
                <input
                  className="input flex-1 pl-13"
                  placeholder="请输入手机号"
                  value={mobile}
                  onChange={e => {
                    setMobile(e.currentTarget.value)
                    if (codeInit) revalidMobile(e.currentTarget.value)
                    else setMobile('')
                  }}
                  onBlur={() => revalidMobile()}
                />
                <Smartphone className="absolute left-4 top-1/2 -translate-y-1/2" />
                {mobileError && (
                  <p className="absolute left-5 bottom-0 translate-y-1/1 text-sm text-destructive">
                    {mobileError}
                  </p>
                )}
              </div>
              <div className="flex-1 flex relative">
                <input
                  className="input flex-1 pl-13"
                  placeholder="请输入验证码"
                  value={code}
                  onChange={e => {
                    setCodeError('')
                    if (!/\d*/.test(e.currentTarget.value)) return
                    setCode(e.currentTarget.value)
                    if (codeInit) revalidCode(e.currentTarget.value)
                  }}
                  onBlur={() => revalidCode()}
                />
                <MessageSquareMore className="absolute left-4 top-1/2 -translate-y-1/2" />
                {codeError && (
                  <p className="absolute left-5 bottom-0 translate-y-1/1 text-sm text-destructive">
                    {codeError}
                  </p>
                )}
                <button
                  className="absolute right-6 top-1/2 -translate-y-1/2
                  text-blue-500 hover:text-blue-500/90 cursor-pointer disabled:text-blue-700"
                  onClick={sendCode}
                  disabled={!!wait}
                >
                  {wait ? `${wait}s` : '获取验证码'}
                </button>
              </div>
            </div>
            <div className="mt-30 flex relative">
              <div
                className="[background:linear-gradient(91.55deg,rgba(0,246,254,0.6)1.6%,rgba(255,106,0,0.6)99.15%)]
                absolute -inset-3 rounded-full blur-xs"
              />
              <button
                className="flex-1 h-[64px] rounded-full bg-black z-10 cursor-pointer"
                onClick={login}
              >
                登录
              </button>
              {error && (
                <p className="absolute left-5 bottom-0 translate-y-2/1 text-sm text-destructive">
                  {error}
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
