import React, { useState, useEffect, useMemo } from 'react'
import { useQueries } from '@tanstack/react-query'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog'
import { useInfiniteQueryProjectList } from '@/hooks/queries/useQueryProject'
import { fetchMaterialDirectoryList } from '@/hooks/queries/useQueryMaterial'
import TreeList from '@/components/TreeList'
import { Plus } from 'lucide-react'
import { ResourceModule } from '@/libs/request/api/resource'
import { SearchInput } from '@/components/ui/search-input'

export enum MoveType {
  MEDIA = 'media',
  FOLDER = 'folder',
  MULTSELECT = 'multiSelect',
}

interface MoveDialogProps {
  open: boolean
  moveId: string
  projectId: number
  moveType: MoveType
  onOpenChange: (open: boolean) => void
  onConfirm: (selectedNode: any) => void
  onCreateDirectory: (
    parentId: string,
    options?: {
      label?: string
      headerTitle?: string
    },
  ) => void
}

const MoveDialog: React.FC<MoveDialogProps> = ({
  open,
  moveId,
  projectId,
  moveType = MoveType.FOLDER,
  onOpenChange,
  onConfirm,
  onCreateDirectory,
}) => {
  const [keyword, setKeyword] = useState('')
  const [selectedNode, setSelectedNode] = useState<any>(null)

  const { data: projectData } = useInfiniteQueryProjectList({})
  const projects = useMemo(() => projectData?.pages.flatMap(page => page.list) || [], [projectData])
  console.log(projectId)

  // 针对每个项目请求目录树
  const projectTrees = useQueries({
    queries: projects.map(project => ({
      queryKey: ['materialDirectoryList', project.id, keyword],
      queryFn: () => fetchMaterialDirectoryList({ projectId: Number(project.id), keyword }),
      enabled: !!projects.length,
    })),
  })

  //请求目录树，适用于我的音乐/我的音效/我的贴纸
  // const { data: treeData } = useQueryMaterialDirectoryList({
  //   projectId: Number(projectId),
  //   keyword,
  // })

  /** 拼接为项目+目录的树结构 */
  const combinedTreeData = useMemo(() => {
    return projects.map((project, index) => ({
      id: `project-${project.id}`,
      label: project.projectName,
      type: 'project', // 用于区分项目节点
      children: projectTrees[index]?.data || [],
    }))
  }, [projects, projectTrees])

  const handleConfirm = async () => {
    if (!selectedNode || selectedNode.type === 'project') return

    if (moveType === MoveType.MEDIA) {
      await ResourceModule.media.move({ fileIds: [moveId], folderUuid: selectedNode.id })
    } else if (moveType === MoveType.FOLDER) {
      await ResourceModule.directory.move({ folderIds: [moveId], parentId: selectedNode.id })
    }

    onConfirm?.(selectedNode)
    onOpenChange(false)
  }

  const actions = [
    {
      icon: <Plus className="w-4 h-4" />,
      label: '新建',
      onClick: (nodeId: string) => {
        onCreateDirectory(nodeId, {
          label: '文件夹名称',
          headerTitle: '文件夹',
        })
      },
    },
  ]

  useEffect(() => {
    if (!open) {
      setKeyword('')
    }
  }, [open])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>移动</DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-auto py-4">
          <SearchInput
            placeholder="关键词搜索"
            value={keyword}
            onChange={e => setKeyword(e.target.value)}
            size="lg"
            containerClassName="mx-4"
          />
          <TreeList
            data={combinedTreeData}
            className="w-full flex-1 max-h-[500px] overflow-auto"
            selectStyle="bg-primary-highlight1"
            actions={actions}
            showEllipsis={false}
            keyword={keyword}
            onSelect={node => {
              setSelectedNode(node)
            }}
          />
        </div>

        <DialogFooter>
          <button
            onClick={() => onOpenChange(false)}
            className="px-4 py-2 bg-primary/20 rounded-md"
          >
            取消
          </button>
          <button
            onClick={handleConfirm}
            className="px-4 py-2 bg-primary-highlight1 text-white rounded-md"
          >
            确定
          </button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default MoveDialog
