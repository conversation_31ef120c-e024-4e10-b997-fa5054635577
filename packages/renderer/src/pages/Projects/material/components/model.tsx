import React, { use<PERSON><PERSON>back, useState, ReactNode } from 'react'
import { genForm } from '@/libs/form'
import { useFormContext } from 'react-hook-form'
import { z } from 'zod'
import { Input } from '@/components/ui/input'
import { useModal, useModalContext } from '@/libs/modal'
import { <PERSON><PERSON><PERSON>oot<PERSON>, ModalHeader } from '@/components/modal'
import * as DIALOG from '@/components/ui/dialog'

export const schema = (label: string = '') =>
  z.object({
    title: z.string().min(1, `请输入${label}`),
  })

type CreateFormProps = {
  label: string
  defaultValues: { title: string }
  onSubmit: (data: any) => Promise<void>
  children?: ReactNode
}

export function NameForm({ label, ...rest }: CreateFormProps) {
  const Form = genForm(schema(label), {
    fields: {
      title: {
        label: '',
        render: ({ field }) => {
          const { setValue } = useFormContext()

          return (
            <div className="relative">
              <Input
                {...field}
                className="pr-16"
                onChange={e => setValue('title', e.currentTarget.value.slice(0, 20))}
              />
              <span className="absolute top-1/2 -translate-y-1/2 right-2 text-muted-foreground text-sm">
                {field.value.length}/20
              </span>
            </div>
          )
        },
      },
    },
  })

  return <Form {...rest} />
}

function NameModal({
  mode,
  title = '',
  label = '',
  headerTitle = '',
  onSubmit,
}: {
  mode: 'create' | 'update'
  title?: string
  label?: string
  headerTitle?: string
  onSubmit: (data: { title: string }) => Promise<void>
}) {
  const { close } = useModalContext()
  const [pending, setPending] = useState(false)

  return (
    <>
      <DIALOG.DialogContent className="bg-gradient-to-tr from-[#101C2C] to-[#002B48] p-6">
        <ModalHeader title={`${mode === 'create' ? '创建' : '重命名'}${headerTitle}`} />
        <NameForm
          label={label}
          defaultValues={{ title: mode === 'create' ? '' : title }}
          onSubmit={async data => {
            setPending(true)
            try {
              await onSubmit({ title: data.title })
              close()
            } finally {
              setPending(false)
            }
          }}
        >
          <ModalFooter pending={pending} />
        </NameForm>
      </DIALOG.DialogContent>
    </>
  )
}

export function useCreateMaterial(
  externalOnSubmit?: (parentId: string, data: Record<string, any>) => Promise<void>,
) {
  const modal = useModal()
  return useCallback(
    (
      parentId: string,
      options?: {
        label?: string
        headerTitle?: string
      },
    ) =>
      modal({
        content: (
          <NameModal
            mode="create"
            label={options?.label ?? ''}
            headerTitle={options?.headerTitle ?? ' '}
            onSubmit={async data => {
              if (externalOnSubmit) {
                await externalOnSubmit(parentId, data)
              } else {
                console.log('未提供 onSubmit')
              }
            }}
          />
        ),
      }),
    [],
  )
}

export function useRenameMaterial(
  externalOnSubmit?: (id: string, title: string, data: Record<string, any>) => Promise<void>,
) {
  const modal = useModal()
  return useCallback(
    (
      id: string,
      title: string,
      options?: {
        label?: string
        headerTitle?: string
      },
    ) =>
      modal({
        content: (
          <NameModal
            mode="update"
            title={title}
            label={options?.label ?? ''}
            headerTitle={options?.headerTitle ?? ' '}
            onSubmit={async data => {
              if (externalOnSubmit) {
                await externalOnSubmit(id, title, data)
              } else {
                console.log('未提供 onSubmit')
              }
            }}
          />
        ),
      }),
    [],
  )
}
