import { lazy } from 'react'
import { create } from 'zustand'
import { nanoid } from 'nanoid'
import { persist } from 'zustand/middleware'
import { pick } from 'lodash'

const SettingsPage = lazy(() => import('@/pages/SettingsPage'))
const EditorPage = lazy(() => import('@/pages/VideoEditor'))
const MixcutPage = lazy(() => import('@/pages/Mixcut/Mixcut.page.tsx'))

export const VirtualizableComponents = {
  Mixcut: MixcutPage,
  Editor: EditorPage,

  /**
   * @deprecated
   */
  Setting: SettingsPage,
} as const

type VirtualizableComponentKeys = keyof typeof VirtualizableComponents

export interface VirtualTab {
  id: string
  componentKey: VirtualizableComponentKeys
  title: string
  keepAlive?: boolean
  closable?: boolean
  params?: Record<string, any>

  // icon?: React.ReactNode

  /**
   * @deprecated
   */
  component?: never
}

interface VirtualTabsStore {
  tabs: VirtualTab[]
  activeTabId: string | null

  // 操作方法
  goToHomePage(): void
  pushTab(tab: Omit<VirtualTab, 'id' | 'title'> & { id?: string; title?: string }): void
  closeTab(id: string): void
  setActiveTab(id: string): void
  updateTab(id: string, updates: Partial<Omit<VirtualTab, 'id'>>): void

  // 批量操作
  closeOtherTabs(id: string): void
  closeAllTabs(): void
  closeTabsToRight(id: string): void
}

function generateTabMeta(tab: Pick<VirtualTab, 'componentKey' | 'params'> & { id?: string }) {
  const { componentKey, params = {} } = tab

  if (componentKey === 'Editor' || componentKey === 'Mixcut') {
    if (!('scriptId' in params)) {
      return null
    }
    const { scriptId } = params

    return {
      id: `${componentKey}-${scriptId}`,
      title: (componentKey === 'Editor' ? '视频编辑' : '视频混剪') + (params.name ? ` - ${params.name}` : '')
    }
  }

  return {
    id: tab.id?.toString() || nanoid(),
    title: null
  }
}

const useVirtualTabsStore = create<VirtualTabsStore>()(
  persist(
    (set, get) => ({
      tabs: [],
      activeTabId: null,

      goToHomePage: () => set({ activeTabId: null }),

      pushTab: tab => {
        const meta = generateTabMeta(tab)
        if (!meta) return

        const { id, title = '' } = meta

        const { closable = true } = tab
        const newTab = { ...tab, id, title, closable } as VirtualTab

        // 检查是否已存在相同 ID 的标签
        const { tabs } = get()
        const existingTabIndex = tabs.findIndex(t => t.id === id)

        set(state => {
          // 如果标签已存在，则激活它
          if (existingTabIndex !== -1) {
            return { ...state, activeTabId: id }
          }

          // 否则添加新标签并激活
          return {
            tabs: [...state.tabs, newTab],
            activeTabId: id,
          }
        })

        return id
      },

      closeTab: id => {
        const { tabs, activeTabId } = get()
        const tabIndex = tabs.findIndex(tab => tab.id === id)

        // 如果标签不存在，直接返回
        if (tabIndex === -1) return

        // 如果关闭的是当前激活的标签，则需要激活其他标签
        let newActiveTabId = activeTabId
        if (activeTabId === id) {
          // 优先激活右侧标签，如果没有则激活左侧标签
          const newActiveIndex = tabIndex === tabs.length - 1
            ? Math.max(0, tabIndex - 1)
            : tabIndex + 1

          newActiveTabId = tabs.length > 1 ? tabs[newActiveIndex].id : null
        }

        set({
          tabs: tabs.filter(tab => tab.id !== id),
          activeTabId: newActiveTabId,
        })
      },

      setActiveTab: id => {
        set({ activeTabId: id })
      },

      updateTab: (id, updates) => {
        set(state => ({
          tabs: state.tabs.map(tab =>
            tab.id === id ? { ...tab, ...updates } : tab,
          ),
        }))
      },

      closeOtherTabs: id => {
        const { tabs } = get()
        const targetTab = tabs.find(tab => tab.id === id)

        if (targetTab) {
          set({
            tabs: [targetTab],
            activeTabId: id,
          })
        }
      },

      closeAllTabs: () => {
        set({
          tabs: [],
          activeTabId: null,
        })
      },

      closeTabsToRight: id => {
        const { tabs } = get()
        const tabIndex = tabs.findIndex(tab => tab.id === id)

        if (tabIndex !== -1) {
          const newTabs = tabs.slice(0, tabIndex + 1)

          set(state => ({
            tabs: newTabs,
            activeTabId: state.activeTabId && newTabs.some(tab => tab.id === state.activeTabId)
              ? state.activeTabId
              : id,
          }))
        }
      },
    }),
    {
      name: 'VirtualTabsStore',
      partialize: state => import.meta.env.DEV ? pick(state, 'tabs', 'activeTabId') : {},
    }
  )
)

export default useVirtualTabsStore
